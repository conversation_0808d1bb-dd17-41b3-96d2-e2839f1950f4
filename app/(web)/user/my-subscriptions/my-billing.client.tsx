"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { UserInfoDB } from "@/@types/user";
import { Card, CardHeader } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AuthError, handleError } from "@/@types/error";
import { ofetch } from "ofetch";
import { round } from "lodash";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { SubmitButton } from "@/components/ui/submit-button";

export default function MyBilling({ user, hasOrder }: { user: UserInfoDB; hasOrder: boolean }) {
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const [handleSubscription, setHandleSubscription] = useState<{
		type: "resume" | "cancel" | "portal" | string;
		subscriptionId?: string;
	} | null>(null);
	const [isGettingCustomerPortalUrl, setIsGettingCustomerPortalUrl] = useState(false);

	const getCustomerPortalUrl = async (type: string, subscriptionId?: string) => {
		if (isGettingCustomerPortalUrl) return;
		try {
			setHandleSubscription({ type, subscriptionId });
			setIsGettingCustomerPortalUrl(true);
			const { status, message, url } = await ofetch("/api/payment/portal", {
				method: "POST",
				body: {},
			});
			handleError(status, message);
			window.open(url, "_blank");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Failed to open customer portal.");
		} finally {
			setHandleSubscription(null);
			setIsGettingCustomerPortalUrl(false);
		}
	};

	return (
		<div className="flex h-full min-h-screen w-full grow flex-col overflow-hidden">
			<ScrollArea className="h-full w-full px-6 md:px-8">
				<div className="mx-auto flex w-full max-w-3xl pb-2 pt-20">
					<h1 className="whitespace-nowrap text-2xl font-medium">My Billing</h1>
				</div>

				<div className="mx-auto flex max-w-3xl flex-col gap-6 pt-4">
					<div className="flex flex-col gap-4">
						<p className="font-medium leading-none tracking-tight text-neutral-700">Credits</p>

						<Card className="w-full rounded-md bg-neutral-50 shadow-none">
							<CardHeader className="px-4 py-0">
								<div className="flex h-[56px] flex-row items-center justify-between gap-2">
									<div className="flex w-full flex-row items-center justify-between gap-1">
										<p className="font-medium leading-none">{round((user?.creditOneTime ?? 0) + (user?.creditFree ?? 0), 1)}</p>

										<Button size="sm" className="bg-teal-500 hover:bg-teal-600" onClick={() => setPlanBoxOpen(true)}>
											Get more
										</Button>
									</div>
								</div>
							</CardHeader>
						</Card>
					</div>

					{hasOrder && (
						<div className="flex flex-col gap-4">
							<p className="font-medium leading-none tracking-tight text-neutral-700">Billing information</p>

							<Card className="w-full rounded-md bg-neutral-50 shadow-none">
								<CardHeader className="px-4 py-2.5">
									<div className="flex flex-row items-center justify-between gap-2">
										<div className="flex flex-row items-center gap-1">
											<p className="text-sm text-neutral-700">{user.email}</p>
										</div>

										<SubmitButton
											isSubmitting={isGettingCustomerPortalUrl && handleSubscription?.type === "portal"}
											variant="ghost"
											onClick={() => getCustomerPortalUrl("portal")}
											className="text-teal-500 hover:text-teal-600"
										>
											Billing history
										</SubmitButton>
									</div>
								</CardHeader>
							</Card>
						</div>
					)}

					{/* <Dialog open={userPlanBoxOpen} onOpenChange={setUserPlanBoxOpen}>
						<DialogTitle className="h-0" />
						<DialogContent className="max-w-max rounded-lg p-0">
							<ScrollArea className="max-h-[90vh] overflow-y-auto" type="always">
								<div className="flex flex-col items-center gap-2 p-6 md:p-8">
									<Plans isUserPlan={true} />
									<Plans />
								</div>
							</ScrollArea>
						</DialogContent>
					</Dialog> */}
				</div>
			</ScrollArea>
		</div>
	);
}
