import { OSS_URL, WEBNA<PERSON> } from "@/lib/constants";
import { Metadata } from "next";
import { AudioLines, Infinity, CheckCircle, Clock, Download, <PERSON><PERSON>he<PERSON>, Zap } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import { BentoItem } from "@/components/landing/features-bento";
import { FeatureBlockTranslation, FeatureBlockSpeaker } from "@/components/landing/feature-block-translation";
import {
	CustomIconsAAC,
	CustomIconsAVI,
	CustomIconsM4A,
	CustomIconsMAV,
	CustomIconsMOV,
	CustomIconsMP3,
	CustomIconsMP4,
	IconsDocument,
	IconsMic,
} from "@/components/icons";
import TranscribeClient from "@/components/app/transcribe.client";
import { Announcement, AnnouncementTitle } from "@/components/ui/kibo-ui/announcement";

export const metadata: Metadata = {
	title: `${WEBNAME} - Transcribe Audio and Video to Accurate Text`,
	description:
		"Transcribe audio and video to text with high accuracy, speed, and speaker recognition. Fast, unlimited audio & video transcription made easy.",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-12 pb-12">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 text-center lg:mt-12">
							<Announcement themed className="mb-4 bg-blue-100 font-medium text-blue-500">
								<AnnouncementTitle>
									3 free transcripts daily. <span className="font-normal">No credit card required.</span>
								</AnnouncementTitle>
							</Announcement>{" "}
							<h1 className="mx-auto max-w-3xl text-3xl font-semibold sm:text-4xl">Unlimited Audio & Video Transcription</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p>
									Convert your audio and video to accurate text in seconds. Our AI-powered tool handles unlimited files, recognizes speakers,
									and delivers ultra-fast transcription with high precision. Perfect for meetings, interviews, lectures, and more.
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4 pb-36">
				<TranscribeClient />
			</div>

			<div className="flex flex-col items-center gap-16 bg-blue-500 px-6 py-20">
				<div className="container text-center">
					<h2 className="text-3xl font-medium text-pretty text-white">Transcribe Audio and Video to Text in 1 Click</h2>
				</div>

				<div className="mx-auto flex w-full max-w-4xl rounded">
					<img
						src={`${OSS_URL}/mkt/home/<USER>
						alt="Transcribe demo with Unoscribe"
						className="h-full w-full rounded-lg md:rounded-2xl"
						loading="lazy"
					/>
				</div>
			</div>

			<HowToUse
				title="How to Transcribe Audio and Video to Text"
				steps={[
					{
						title: "1. Upload audio or video",
						description:
							"Select the audio or video file you want to transcribe. Our platform supports all popular file formats for fast and secure uploads.",
					},
					{
						title: "2. Transcribe",
						description:
							"Let our AI transcribe your audio or video to accurate text in seconds. Enjoy ultra-fast processing with high accuracy and automatic speaker recognition.",
					},
					{
						title: "3. Download transcript",
						description: "Get your completed transcription instantly. Download your text in your preferred format—TXT, MD, PDF, DOCX, or SRT.",
					},
				]}
			/>

			<div className="container px-6 py-20">
				<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-9">
					<BentoItem
						title="Automatic transcription"
						description="Turn your audio and video files into clear, precise text in seconds. Our AI-powered audio transcription technology delivers top-level accuracy, so you spend less time editing and more time sharing your message."
						className="col-span-1 md:col-span-5"
					>
						<div className="mt-8 flex min-h-[160px] flex-1 flex-row items-center justify-center gap-6 rounded-2xl bg-zinc-50 sm:gap-3 md:gap-6 lg:gap-8">
							<IconsMic className="size-20 text-blue-400/80 md:size-[100px]" />
							<AudioLines absoluteStrokeWidth className="size-12 text-blue-200 md:size-16" />
							<IconsDocument className="size-20 text-blue-400/80 md:size-[100px]" />
						</div>
					</BentoItem>

					<BentoItem
						title="100+ Languages"
						description="Reach a global audience with confidence. Easily transcribe audio to text or video to text in over 100 languages, making your content accessible no matter where your viewers or listeners are in the world."
						className="col-span-1 md:col-span-4"
					>
						<FeatureBlockTranslation />
					</BentoItem>

					<BentoItem
						title="Speaker Recognition"
						description="Know exactly who said what. Our AI detects and labels different speakers automatically, making your transcripts easy to read and keeping conversations clear in every transcribed file."
						className="col-span-1 md:col-span-4"
					>
						<FeatureBlockSpeaker />
					</BentoItem>

					<BentoItem
						title="Variety of formats"
						description="No need to worry about compatibility. Transcribe from any popular audio or video format, supporting your workflow and making video transcription as easy as possible."
						className="col-span-1 md:col-span-5"
					>
						{/* <FeatureBlockFileFormats /> */}
						<div className="mt-8 flex flex-row flex-wrap items-center gap-4">
							<CustomIconsMP3 />
							<CustomIconsMAV />
							<CustomIconsAAC />
							<CustomIconsM4A />
							<CustomIconsMP4 />
							<CustomIconsMOV />
							<CustomIconsAVI />
						</div>
					</BentoItem>
				</div>
			</div>

			<GridSections
				title="Features of Audio & Video Transcription"
				sections={[
					{
						title: "High Accuracy",
						description: "Achieve up to 99.8% accuracy when you transcribe audio to text or video to text, even with multiple speakers.",
						icon: CheckCircle,
					},
					{
						title: "Unlimited Transcription",
						description: "Transcribe audio and video files as many times as you need—no limits on how often you transcribe.",
						icon: Infinity,
					},
					{
						title: "Multiple Export Formats",
						description: "Download your audio and video transcripts as TXT, PDF, DOCX, SRT, or Markdown for easy sharing or editing.",
						icon: Download,
					},
					{
						title: "Ultra-fast",
						description: "Get fast video and audio transcription without the wait, thanks to the power of AI.",
						icon: Zap,
					},
					{
						title: "Save Time",
						description: "Automate transcription to save hours, so you can focus on more important work.",
						icon: Clock,
					},
					{
						title: "Private & Secure",
						description: "Your audio, video, and transcripts are kept safe and private with robust encryption.",
						icon: ShieldCheck,
					},
				]}
			/>

			<FAQsComponent
				title="Audio & Video Transcription Related FAQs"
				faqs={[
					{
						question: "What is Unoscribe?",
						answer: "Unoscribe is an AI-powered transcription service that lets you transcribe audio and video to accurate text. It supports over 100 languages, offers high accuracy, ultra-fast results, and features advanced speaker recognition. With Unoscribe, you can easily turn your audio and video files into high-quality transcripts.",
					},
					{
						question: "Can I use Unoscribe for free?",
						answer: "Yes! Unoscribe offers a free plan that gives you three transcription sessions every day, up to 30 minutes each. You can try all basic features for free, and you can upgrade anytime to unlock unlimited transcriptions and advanced tools.",
					},
					{
						question: "Is Unoscribe really unlimited?",
						answer: "Yes, Unoscribe Pro members enjoy truly unlimited audio and video transcription. Transcribe as many files as you need, whenever you need, without limits. Perfect for heavy users and businesses.",
					},
					{
						question: "Is my data safe with Unoscribe?",
						answer: "Absolutely. Your uploaded files and generated transcripts are private and only visible to you. We use strong security measures to protect your data, so you can trust your information is always safe.",
					},
					{
						question: "What audio and video formats does Unoscribe support?",
						answer: "Unoscribe supports all popular formats. For audio: MP3, WAV, AAC, WMA, OGG, M4A, and FLAC. For video: MP4, MOV, AVI, MKV, WMV, FLV, and WebM. Just upload your audio or video files and start transcribing with ease.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Start Transcribing for Free"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
				title="Transcribe Audio and Video in Seconds"
				description="Convert your audio and video files to accurate text with AI-powered transcription. Enjoy high accuracy, speaker recognition, and ultra-fast results—perfect for all your transcription needs."
			/>
		</main>
	);
}
