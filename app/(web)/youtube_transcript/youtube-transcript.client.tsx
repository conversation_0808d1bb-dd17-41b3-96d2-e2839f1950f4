"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export default function YouTubeTranscriptClient() {
	const [videoUrl, setVideoUrl] = useState("");
	const [transcript, setTranscript] = useState("");
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState("");

	const handleGenerateTranscript = async () => {
		setLoading(true);
		setError("");
		setTranscript("");

		try {
			const response = await fetch("/api/v1/youtube_transcript", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ videoUrl }),
			});

			if (!response.ok) {
				throw new Error("Failed to generate transcript");
			}

			const data = await response.json();
			setTranscript(data.transcript);
		} catch (err: any) {
			setError(err.message || "An error occurred");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="bg-muted mx-auto mt-4 rounded-xl border p-4">
			<Input type="text" placeholder="Enter YouTube Video URL" value={videoUrl} onChange={(e) => setVideoUrl(e.target.value)} />
			<Button onClick={handleGenerateTranscript} disabled={loading || !videoUrl}>
				{loading ? "Generating..." : "Generate Transcript"}
			</Button>
			{error && <p style={{ color: "red" }}>Error: {error}</p>}
			{transcript && (
				<div>
					<h2>Transcript:</h2>
					<pre>{transcript}</pre>
				</div>
			)}
		</div>
	);
}
