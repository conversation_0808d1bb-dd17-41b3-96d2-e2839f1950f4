"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SparklesIcon, Copy, LucideIcon, File, Link, ChevronDown, AlignLeft, ChevronLeft, Crown } from "lucide-react";
import { SubmitButton } from "@/components/ui/submit-button";
import { ofetch } from "ofetch";
import { AuthError, FreeLimitError, handleError, IgnoreError } from "@/@types/error";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getLanguageName, getLanguages } from "@/lib/languages";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useCopyToClipboard } from "usehooks-ts";
import { Hint } from "@/components/ui/custom/hint";
import { useSession } from "@/lib/auth-client";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useUserStore } from "@/store/useUserStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { MembershipID } from "@/@types/membership-type";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useCookies } from "next-client-cookies";
import { useSubmitTimer1 } from "@/hooks/use-submit-timer-1";
import { MediaProvider } from "media-chrome/react/media-store";
import { ExportDropdown } from "@/components/app/export-transcript";
import ExtractFileClient from "@/components/app/extract-file.client";
import { Separator } from "@/components/ui/separator";
import { Spinner } from "@/components/ui/kibo-ui/spinner";
import ProjectTranscriptClient from "@/components/app/project-transcript.client";
import MediaPlayer from "@/components/app/media-player";

// const ExtractFileClient = dynamic(() => import("@/components/app/extract-file.client"), {
// 	ssr: false,
// 	loading: () => (
// 		<div className="relative h-[142px] w-full flex-shrink-0">
// 			<Skeleton className="h-full bg-white" />
// 			<p className="text-muted-foreground absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-sm">Loading...</p>
// 		</div>
// 	),
// });

const languages = getLanguages.sort((a, b) => a.name.localeCompare(b.name));

type FileSourceType = {
	id: string;
	name: string;
	icon: LucideIcon;
};
const fileTypes: FileSourceType[] = [
	{
		id: "file",
		name: "File",
		icon: File,
	},
	{
		id: "url",
		name: "URL",
		icon: Link,
	},
];

export default function TranscribeDemoClient({ initialFileType = "file", hookText }: { initialFileType?: string; hookText?: string }) {
	const COOKIE_KEY_RECENT_LANGUAGE = "recent_language";
	const cookies = useCookies();
	const { data: session } = useSession();
	const { user, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const [copiedText, copy] = useCopyToClipboard();
	const onCopyFromSentences = (sentences: SentenceData[]) => {
		const content = sentences.map((sentence) => sentence.text).join(" ");
		copy(content)
			.then(() => {
				toast.success("Transcript copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};

	// ======== Upload & Transcribe Configuration ========
	const [languageCode, setLanguageCode] = useState<string>("en");
	const [languageOpen, setLanguageOpen] = useState<boolean>(false);
	const [recentLanguageCodes, setRecentLanguageCodes] = useState<string[]>([]);
	const handleInitialRecentLanguage = () => {
		const recentLanguageCodesString = cookies.get(COOKIE_KEY_RECENT_LANGUAGE);
		if (!recentLanguageCodesString) return;
		const recentLanguageCodes = recentLanguageCodesString.split(",");
		setRecentLanguageCodes(recentLanguageCodes);
	};
	const handleAddNewRecentLanguage = (languageCode: string) => {
		if (!languageCode) return;
		if (recentLanguageCodes.includes(languageCode)) return;
		const newRecentLanguageCode = [languageCode, ...recentLanguageCodes];
		setRecentLanguageCodes(newRecentLanguageCode);
		cookies.set(COOKIE_KEY_RECENT_LANGUAGE, newRecentLanguageCode.join(","), {
			path: "/",
		});
	};
	useEffect(() => {
		handleInitialRecentLanguage();
	}, []);

	const [fileSourceType, setFileSourceType] = useState<FileSourceType>(fileTypes.find((type) => type.id === initialFileType) || fileTypes[0]);
	const [configSpeaker, setConfigSpeaker] = useState<boolean>(false);
	const [selectFile, setSelectFile] = useState<{
		fileName: string;
		fileDuration: number;
		fileUrl: string;
		fileType: string;
	} | null>(null);

	const [submitting, setSubmitting] = useState(false);
	// const [sentencesData, setSentencesData] = useState<{
	// 	sentences: SentenceData[];
	// 	diarize: boolean;
	// } | null>(null);
	const [sentencesData, setSentencesData] = useState<{
		sentences: SentenceData[];
		diarize: boolean;
	} | null>({
		sentences: [
			{
				text: "1",
				start: 0,
				end: 100,
			},
		],
		diarize: false,
	});
	const { seconds } = useSubmitTimer1(submitting);
	const handleFileTranscribe = async () => {
		void handleAddNewRecentLanguage(languageCode);
		if (submitting) return;
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		try {
			setSubmitting(true);
			setSentencesData(null);
			const {
				status,
				message,
				sentences: sentencesResult,
				diarize,
			} = await ofetch("/api/v1/transcribe", {
				method: "POST",
				body: {
					source: fileSourceType.id,
					fileUrl: selectFile?.fileUrl,
					fileName: selectFile?.fileName,
					lang: languageCode,
					speaker: configSpeaker,
					d: selectFile?.fileDuration,
				},
			});
			handleError(status, message);
			setSentencesData({
				sentences: sentencesResult,
				diarize,
			});

			toast.success("Transcription completed.");
		} catch (error: any) {
			console.error("Failed to transcribe file:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof FreeLimitError) {
				toast.warning(error.message, {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			// if (error instanceof Credits402Error) {
			// 	toast.warning("You do not have enough credits.", {
			// 		action: {
			// 			label: "Get more",
			// 			onClick: () => setPlanBoxOpen(true),
			// 		},
			// 	});
			// 	return;
			// }
			toast.error("Transcription failed.");
		} finally {
			setSubmitting(false);
		}
	};

	return (
		<>
			{sentencesData ? (
				<div className="bg-muted mx-auto mt-4 rounded-xl border p-4" id="transcript">
					<div className="flex flex-col rounded-xl bg-white">
						<MediaProvider>
							<div className="flex flex-row items-center justify-between gap-1 border-b px-4 py-2">
								<div className="text-muted-foreground flex flex-row items-center gap-1">
									<Button size="sm" className="mr-1 flex-shrink-0 cursor-pointer" onClick={() => setSentencesData(null)}>
										<ChevronLeft className="size-4" />
										New
									</Button>
									<div className="hidden flex-row items-center gap-1 sm:flex">
										<AlignLeft className="size-4" />
										<p className="text-sm">Transcript</p>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<Hint label="Copy transcript" sideOffset={10} className="">
										<Button
											className="flex-shrink-0 cursor-pointer"
											type="button"
											size="icon"
											variant="ghost"
											onClick={() => onCopyFromSentences(sentencesData.sentences)}
										>
											<Copy className="h-4 w-4" />
										</Button>
									</Hint>
									<ExportDropdown
										sentences={sentencesData.sentences}
										filename={selectFile?.fileName || "transcript"}
										languageCode={languageCode}
										userHasPaid={userHasPaid}
										size="sm"
									/>
								</div>
							</div>

							<div className={cn("flex w-full grow flex-col font-[380] text-zinc-800")}>
								<ProjectTranscriptClient sentences={sentencesData.sentences} diarize={sentencesData.diarize} />
							</div>
							{!selectFile && (
								<div className="px-4 py-0.5">
									<MediaPlayer
										// fileUrl={selectFile.fileUrl}
										fileUrl="https://static.unoscribe.com/source-free/202507/20250713-019803d4ae2b72279db93d3ada260859.mp3"
									/>
								</div>
							)}
						</MediaProvider>
					</div>
				</div>
			) : (
				<>
					{/* <Tabs className="mx-auto" value={fileSourceType.id}>
                        <TabsList className="gap-1 rounded-xl border bg-white">
                            {fileTypes.map((type, index) => (
                                <TabsTrigger
                                    key={index}
                                    value={type.id}
                                    onClick={() => setFileSourceType(type)}
                                    className="cursor-pointer gap-1.5 rounded-lg data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
                                >
                                    <type.icon className="size-4" />
                                    {type.name}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    </Tabs> */}
					<div className={cn("bg-muted mx-auto max-w-[800px] rounded-2xl border shadow-lg", user?.membershipId === MembershipID.Free && "h-[428px]")}>
						<div className="flex h-full w-full flex-col gap-2 p-2">
							{user?.membershipId === MembershipID.Free && (
								<div className="flex flex-row items-center justify-between gap-1 rounded-lg bg-white p-3">
									<div className="flex flex-col items-start gap-1 text-xs text-zinc-700 sm:flex-row sm:items-center">
										<p className="flex flex-row items-center gap-0.5">
											<Crown className="size-3.5 text-yellow-500" />
											Unlimited transcriptions
										</p>
										<Separator orientation="vertical" className="hidden data-[orientation=vertical]:h-[16px] sm:block" />
										<p className="flex flex-row items-center gap-0.5">
											<Crown className="size-3.5 text-yellow-500" />
											Speaker recongnition
										</p>
										<Separator orientation="vertical" className="hidden data-[orientation=vertical]:h-[16px] sm:block" />
										<p className="flex flex-row items-center gap-0.5">
											<Crown className="size-3.5 text-yellow-500" />5 hours/file
										</p>
									</div>
									<Button size="sm" variant="outline" className="cursor-pointer text-xs" onClick={() => setPlanBoxOpen(true)}>
										<Crown className="size-3.5 text-yellow-500" />
										Go unlimited
									</Button>
								</div>
							)}

							{submitting ? (
								<div className="flex h-full min-h-[256px] flex-1 flex-col justify-center rounded-lg border bg-white px-4 py-6 text-center">
									<Spinner variant="ellipsis" className="mx-auto text-blue-500" />
									<p className="mt-3">
										Transcribing...<span className="text-muted-foreground ml-1 text-sm tabular-nums">[{seconds}s]</span>
									</p>
									<p className="text-muted-foreground mt-2 text-center text-sm font-[350]">
										Please wait while we transcribe your file. This may take a few seconds, depending on its length.
									</p>
								</div>
							) : (
								<>
									{fileSourceType.id === "file" && (
										<div className="flex h-full flex-1 flex-col space-y-1.5">
											<ExtractFileClient
												submitting={submitting}
												selectFile={selectFile}
												setSelectFile={setSelectFile}
												hookText={hookText}
											/>

											{selectFile && (
												<>
													<div className="space-y-3 rounded-xl border bg-white px-1.5 py-3">
														<div className="space-y-2">
															<p className="flex flex-row items-center">
																<span className="text-destructive mr-1 w-2 text-sm font-light">*</span>
																<span className="text-sm">Audio language</span>
															</p>
															<div className="pl-3">
																<Popover open={languageOpen} onOpenChange={setLanguageOpen}>
																	<PopoverTrigger asChild>
																		<Button
																			variant="outline"
																			size="default"
																			role="combobox"
																			aria-expanded={languageOpen}
																			className="w-full cursor-pointer justify-between px-3 font-[380] text-zinc-700 shadow-none sm:w-[200px]"
																		>
																			{getLanguageName(languageCode) || "Language"}
																			<ChevronDown className="text-zinc-400" />
																		</Button>
																	</PopoverTrigger>
																	<PopoverContent className="w-[220px] p-0">
																		<Command>
																			<CommandInput placeholder="Search Language" />
																			<CommandEmpty>No Language found.</CommandEmpty>
																			<CommandGroup className="p-0 py-1">
																				<ScrollArea className="h-72" type="always">
																					{recentLanguageCodes && recentLanguageCodes.length > 0 && (
																						<>
																							<p className="text-muted-foreground px-2 py-1 text-xs font-[380]">
																								Recently used
																							</p>
																							{recentLanguageCodes.map((languageItem) => (
																								<CommandItem
																									key={languageItem}
																									value={getLanguageName(languageItem)}
																									onSelect={() => {
																										setLanguageCode(languageItem);
																										setLanguageOpen(false);
																									}}
																									className={cn(
																										"mx-1 my-1 cursor-pointer justify-between text-zinc-700",
																										languageCode === languageItem &&
																											"bg-accent text-accent-foreground",
																									)}
																								>
																									{getLanguageName(languageItem)}
																								</CommandItem>
																							))}
																							<p className="text-muted-foreground mt-2 px-2 py-1 text-xs font-[380]">
																								All
																							</p>
																						</>
																					)}
																					{languages.map((languageItem) => {
																						if (recentLanguageCodes.includes(languageItem.value)) {
																							return null;
																						}
																						return (
																							<CommandItem
																								key={languageItem.value}
																								value={languageItem.name}
																								onSelect={() => {
																									setLanguageCode(languageItem.value);
																									setLanguageOpen(false);
																								}}
																								className={cn(
																									"mx-1 my-1 cursor-pointer justify-between text-zinc-700",
																									languageCode === languageItem.value &&
																										"bg-accent text-accent-foreground",
																								)}
																							>
																								{languageItem.name}
																							</CommandItem>
																						);
																					})}
																				</ScrollArea>
																			</CommandGroup>
																		</Command>
																	</PopoverContent>
																</Popover>
															</div>
														</div>
														<div className="space-y-2">
															<p className="flex flex-row items-center">
																<span className="text-destructive mr-1 w-2 text-sm font-light">*</span>
																<span className="text-sm">Configure speaker recognition</span>
															</p>
															<div className="flex flex-row items-center pl-3 text-zinc-600">
																<RadioGroup
																	defaultValue="false"
																	className="gap-1.5"
																	value={configSpeaker ? "true" : "false"}
																	onValueChange={(value) => {
																		if (value === "true" && !userHasPaid) {
																			setPlanBoxOpen(true);
																			return;
																		}
																		setConfigSpeaker(value === "true");
																	}}
																>
																	<div className="flex items-center space-x-2">
																		<RadioGroupItem value="false" id="no-speaker-recognition" className="cursor-pointer" />
																		<Label
																			htmlFor="no-speaker-recognition"
																			className="cursor-pointer text-[13px] font-[380]"
																		>
																			No speaker recognition
																		</Label>
																	</div>
																	<div className="flex items-center space-x-2">
																		<RadioGroupItem value="true" id="recognize-speakers" className="cursor-pointer" />
																		<Label htmlFor="recognize-speakers" className="cursor-pointer text-[13px] font-[380]">
																			Recognize Speakers
																		</Label>
																	</div>
																</RadioGroup>
															</div>
														</div>
													</div>
													<div className="mt-0.5 flex flex-row flex-wrap items-center justify-center gap-3">
														<SubmitButton
															size="lg"
															isSubmitting={submitting}
															variant="default"
															disabled={submitting || !selectFile}
															className="w-[140px] cursor-pointer rounded-full bg-blue-500 hover:bg-blue-500"
															onClick={handleFileTranscribe}
														>
															<SparklesIcon />
															Transcribe
														</SubmitButton>
													</div>
												</>
											)}
										</div>
									)}
								</>
							)}
						</div>
					</div>
				</>
			)}
		</>
	);
}
