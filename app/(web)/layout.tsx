// import { serverSideTranslation } from '@/lib/i18n'
// import { i18nConfig } from "@/i18n-config";
import { Header } from "@/components/navigate/header";
import Footer from "@/components/navigate/footer";
import FooterBadge from "@/components/navigate/footer-badge";
import { InitializeUser } from "@/components/shared/initialize-user";
import PlanDialog from "@/components/shared/plan-dialog";
import SignInDialog from "@/components/shared/sigin-in-dialog";
import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoolge } from "@/components/analytics/analytics-google";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { UserInfoDB } from "@/@types/user";
import { refreshUser } from "@/server/refresh-user";

// type Params = Promise<{ lang: string }>;
const getUser = async () => {
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return null;
	}
	const userId = sessionUser.id;
	const userInfo: UserInfoDB | null = await refreshUser(userId);
	return userInfo;
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	// const { t } = await serverSideTranslation(lang, 'common')
	const userInfo = await getUser();
	return (
		<div className="flex min-h-screen flex-col">
			<Header />
			{children}
			<Footer />
			{/* <FooterBadge /> */}
			<InitializeUser userInfo={userInfo} />
			<PlanDialog />
			<SignInDialog />
			<AnalyticsClarity />
			<AnalyticsGoolge />
		</div>
	);
}
