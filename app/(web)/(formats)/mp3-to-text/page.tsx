import { OSS_URL, WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import { AudioLines, Zap, <PERSON>em, FileText, ShieldCheck, Infinity, Smile } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import { BentoItem } from "@/components/landing/features-bento";
import { FeatureBlockTranslation, FeatureBlockSpeaker } from "@/components/landing/feature-block-translation";
import {
	CustomIconsAAC,
	CustomIconsAVI,
	CustomIconsM4A,
	CustomIconsMAV,
	CustomIconsMOV,
	CustomIconsMP3,
	CustomIconsMP4,
	IconsDocument,
	IconsMic,
} from "@/components/icons";
import TranscribeClient from "@/components/app/transcribe.client";

export const metadata: Metadata = {
	title: `MP3 to Text Converter - Transcribe MP3 to Text Free | ${WEBNAME}`,
	description:
		"Convert mp3 to text in seconds with high accuracy. 100+ languages supported. Free to try, private, simple, and lightning fast. Unoscribe, your trusted mp3 to text converter.",
	alternates: {
		canonical: "/mp3-to-text",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-12 pb-12">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 text-center lg:mt-16">
							<h1 className="mx-auto max-w-3xl text-3xl font-semibold sm:text-4xl">Convert MP3 to Text in Seconds, Not Hours</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p>
									Transcribe any MP3 file into precise, well-structured text in seconds. No technical skills needed—just upload, and get
									reliable, high-accuracy transcripts in over 100 languages. Free to try, this simple online MP3 to text converter saves you
									hours, delivers trustworthy results
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4 pb-36">
				<TranscribeClient hookText="Click or drag & drop to upload your audio file." />
			</div>

			<div className="flex flex-col items-center gap-16 bg-blue-500 px-6 py-32">
				<div className="container text-center">
					<h2 className="text-3xl font-medium text-pretty text-white">Transcribe MP3 to Accurate Text in 1 Click</h2>
				</div>

				<div className="mx-auto flex w-full max-w-4xl rounded">
					<img
						src={`${OSS_URL}/mkt/home/<USER>
						alt="Transcribe demo with Unoscribe"
						className="h-full w-full rounded-lg md:rounded-2xl"
						loading="lazy"
					/>
				</div>
			</div>

			<HowToUse
				title="How to convert MP3 to text?"
				steps={[
					{
						title: "1. Upload Your MP3 File",
						description:
							"Drag and drop your mp3 file into our secure upload area or pick it from your device. No technical skills required – just a simple upload to get started.",
					},
					{
						title: "2. Choose Your Language",
						description:
							"Select from over 100 languages. Our AI supports global voices, so your mp3 to text conversion is accurate—no matter the language.",
					},
					{
						title: "3. Transcribe",
						description:
							"Hit 'Transcribe' and let our powerful AI go to work. It listens closely, detects speakers, and converts your mp3 to text quickly and precisely.",
					},
					{
						title: "4. Download in Multiple Formats",
						description:
							"Get your text instantly. Download, copy, or export your transcription in TXT, PDF, DOCX, SRT, or MD formats—perfect for editing, publishing, or sharing.",
					},
				]}
			/>

			<div className="container px-6 py-20">
				<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-9">
					<BentoItem
						title="Fast transcription"
						description="Say goodbye to wasted hours and frustrating errors. With Unoscribe, convert MP3 to text instantly—and experience a new standard in convenience and reliability. Discover a smarter way to capture every word, every time."
						className="col-span-1 border md:col-span-5"
					>
						<div className="mt-8 flex min-h-[160px] flex-1 flex-row items-center justify-center gap-6 rounded-2xl bg-zinc-50 sm:gap-3 md:gap-6 lg:gap-8">
							<IconsMic className="size-20 text-blue-400/80 md:size-[100px]" />
							<AudioLines absoluteStrokeWidth className="size-12 text-blue-200 md:size-16" />
							<IconsDocument className="size-20 text-blue-400/80 md:size-[100px]" />
						</div>
					</BentoItem>

					<BentoItem
						title="100+ Languages"
						description="No more worrying about errors or language barriers. Our advanced AI delivers near-perfect transcription accuracy in over 100 languages, ensuring your text is reliable and ready for any project."
						className="col-span-1 border md:col-span-4"
					>
						<FeatureBlockTranslation />
					</BentoItem>

					<BentoItem
						title="Speaker Recognition"
						description="Every speaker’s words are automatically separated and clearly labeled in your mp3 to text transcription. Easily follow multi-person conversations in meetings, interviews, and lectures, making transcripts much easier to review and reference."
						className="col-span-1 border md:col-span-4"
					>
						<FeatureBlockSpeaker />
					</BentoItem>

					<BentoItem
						title="Variety of formats"
						description="Convert more than just mp3 to text—transcribe audio from WAV, M4A, AAC, and other formats easily. No need to convert your files beforehand. Enjoy greater flexibility and compatibility for all your transcription needs."
						className="col-span-1 border md:col-span-5"
					>
						{/* <FeatureBlockFileFormats /> */}
						<div className="mt-8 flex flex-row flex-wrap items-center gap-4">
							<CustomIconsMP3 />
							<CustomIconsMAV />
							<CustomIconsAAC />
							<CustomIconsM4A />
							<CustomIconsMP4 />
							<CustomIconsMOV />
							<CustomIconsAVI />
						</div>
					</BentoItem>
				</div>
			</div>

			<GridSections
				title="Features of MP3 to Text Transcription"
				sections={[
					{
						title: "Lightning Speed",
						description: "Transcribe your MP3 files in seconds, saving you hours of manual work.",
						icon: Zap,
					},
					{
						title: "99.8% Accuracy",
						description: "Our AI delivers near-perfect transcription results you can rely on.",
						icon: Gem,
					},
					{
						title: "Unlimited Transcriptions",
						description: "Convert as many MP3 files to text as you need—no hidden limits.",
						icon: Infinity,
					},
					{
						title: "Multiple Export Formats",
						description: "Export your text as TXT, PDF, DOCX, SRT, or MD to suit any need.",
						icon: FileText,
					},
					{
						title: "Private & Secure",
						description: "Your files and transcripts are kept secure and private, always.",
						icon: ShieldCheck,
					},
					{
						title: "User-Friendly",
						description: "No tech skills required. Clean, simple interface everyone can use.",
						icon: Smile,
					},
				]}
			/>

			<FAQsComponent
				title="MP3 to Text Transcription Related FAQs"
				faqs={[
					{
						question: "What is an mp3 to text converter?",
						answer: "An mp3 to text converter is a tool, like Unoscribe, that uses Artificial Intelligence (AI) to listen to an audio file (like an mp3) and automatically write out everything that is said into a text document. It saves you from having to manually type out the conversation yourself, which is a slow and difficult process.",
					},
					{
						question: "Is the Mp3 to text converter really free to try?",
						answer: "Yes, absolutely. We believe you should see the power of our MP3 to text conversion tool for yourself. You can sign up and convert your first audio file completely free to test the speed and accuracy. No credit card is required to get started.",
					},
					{
						question: "How do I convert my mp3 to text?",
						answer: "It's incredibly simple and designed for everyone, regardless of technical skill. Just upload your mp3 file, click the \"Transcribe\" button, and download your completed text file in your desired format. That's it!",
					},
					{
						question: "How accurate are the transcriptions?",
						answer: "Our AI engine is trained on a massive dataset of audio, allowing it to reach up to 99.8% accuracy in optimal conditions (clear audio, minimal background noise). This is near-human level and significantly reduces the time you'll need to spend on proofreading and editing compared to other services or manual work.",
					},
					{
						question: "What formats can I export my transcript to?",
						answer: "We support a wide variety of the most common formats to fit your needs. You can export your transcript as a plain text file (.TXT), a Microsoft Word document (.DOCX), a Portable Document Format file (.PDF), a subtitle file for video editing (.SRT), or a Markdown file (.MD).",
					},
					{
						question: "What languages are supported?",
						answer: "We proudly support transcription in over 100 languages and dialects from around the world. This includes major languages like English (US, UK, AU), Spanish, French, German, Mandarin, and Japanese, as well as many others.",
					},
					{
						question: "How can I convert an audio file directly into a Word document?",
						answer: 'It\'s as easy as selecting an option. After Unoscribe converts your mp3 to text, you will see an "Export" button. Simply click it and choose the DOCX format. Your transcript will be downloaded as a fully formatted Microsoft Word document, ready for you to use.',
					},
					{
						question: "Is my data safe and private?",
						answer: "We take your privacy and security extremely seriously. Only you have access to the files you upload and the transcripts generated. The entire transcription process is fully automated, so no human ever sees your files or transcripts. Your data is yours alone, and we are committed to keeping it that way.",
					},
					{
						question: "Does it recognize different speakers?",
						answer: "Yes. Our advanced speaker recognition tags every speaker, making group conversations, interviews, and meetings crystal clear in your transcript.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Transcribe MP3 to Text Now"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
				title="Convert MP3 to Text Instantly—Try for Free"
				description="Free yourself from tedious manual typing. Experience instant, accurate, private transcription—no stress, no complexity—just reliable results in seconds. Discover how simple converting MP3 to text can be!"
			/>
		</main>
	);
}
