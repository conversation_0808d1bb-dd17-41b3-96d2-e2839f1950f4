import { OSS_URL, WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import { AudioLines, Zap, <PERSON>em, FileText, ShieldCheck, Infinity, Smile } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import { BentoItem } from "@/components/landing/features-bento";
import { FeatureBlockTranslation, FeatureBlockSpeaker } from "@/components/landing/feature-block-translation";
import {
	CustomIconsAAC,
	CustomIconsAVI,
	CustomIconsM4A,
	CustomIconsMAV,
	CustomIconsMOV,
	CustomIconsMP3,
	CustomIconsMP4,
	IconsDocument,
	IconsMic,
} from "@/components/icons";
import TranscribeClient from "@/components/app/transcribe.client";

export const metadata: Metadata = {
	title: `M4A to Text - Transcribe M4A to Text Online Free | ${WEBNAME}`,
	description: "Transcribe M4A to text instantly online with high accuracy. Fast, easy M4A to text conversion. 100+ languages supported. Try it free now!",
	alternates: {
		canonical: "/m4a-to-text",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-12 pb-12">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 text-center lg:mt-16">
							<h1 className="mx-auto max-w-3xl text-3xl font-semibold sm:text-4xl">Transcribe M4A to Text in Seconds</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p>
									Transcribe your M4A files to flawless, readable text instantly. Enjoy high accuracy, fast results, and support for more than
									100 languages—making your content easy to search, share, and understand.
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4 pb-36">
				<TranscribeClient hookText="Click or drag & drop to upload your audio file." />
			</div>

			<div className="flex flex-col items-center gap-16 bg-blue-500 px-6 py-32">
				<div className="container text-center">
					<h2 className="text-3xl font-medium text-pretty text-white">Transcribe M4A to Accurate Text in 1 Click</h2>
				</div>

				<div className="mx-auto flex w-full max-w-4xl rounded">
					<img
						src={`${OSS_URL}/mkt/home/<USER>
						alt="Transcribe demo with Unoscribe"
						className="h-full w-full rounded-lg md:rounded-2xl"
						loading="lazy"
					/>
				</div>
			</div>

			<HowToUse
				title="How to Transcribe M4A to text?"
				steps={[
					{
						title: "1. Upload Your M4A File",
						description:
							"Simply drag and drop your M4A file, or click to upload directly from your device. Whether it’s an interview, a business meeting, a lecture, or a personal recording, our platform is ready to handle your audio securely and privately.",
					},
					{
						title: "2. Choose Language",
						description:
							"We support over 100 languages, so you can transcribe M4A to text regardless of your native tongue. Select your preferred language for the most accurate results, including speaker identification—perfect for files with multiple voices.",
					},
					{
						title: "3. Transcribe",
						description:
							'Just click the "Transcribe" button—there’s no need to wait or worry. Our advanced technology will instantly convert your M4A file to accurate text. You’ll see your spoken words turned to written content in seconds, complete with precise timestamps for effortless navigation.',
					},
					{
						title: "4. Download transcript",
						description:
							"Once your M4A is transcribed to text, export the results in the format that works for you: TXT, PDF, DOCX, SRT, MD, and more. Enjoy your text with accurate timestamps, making it easy to reference, edit, or share—always keeping your data private and secure.",
					},
				]}
			/>

			<div className="container px-6 py-20">
				<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-9">
					<BentoItem
						title="Ultra-Fast Transcription"
						description="Transcribe your M4A files into clear, accurate text in just seconds. No more waiting hours for manual typing or complex software. Our AI-powered engine delivers immediate results, so you stay focused on what matters most—your thoughts, stories, and ideas. Transcribe M4A to text at lightning speed and reclaim your valuable time."
						className="col-span-1 border md:col-span-5"
					>
						<div className="mt-8 flex min-h-[160px] flex-1 flex-row items-center justify-center gap-6 rounded-2xl bg-zinc-50 sm:gap-3 md:gap-6 lg:gap-8">
							<IconsMic className="size-20 text-blue-400/80 md:size-[100px]" />
							<AudioLines absoluteStrokeWidth className="size-12 text-blue-200 md:size-16" />
							<IconsDocument className="size-20 text-blue-400/80 md:size-[100px]" />
						</div>
					</BentoItem>

					<BentoItem
						title="100+ Languages"
						description="Break language barriers instantly—our tool understands and transcribes M4A to text in over 100 languages. Whether you’re recording interviews, lectures, meetings, or creative work, you’ll always get readable, accurate transcripts in your preferred language. Seamless global communication is finally at your fingertips."
						className="col-span-1 border md:col-span-4"
					>
						<FeatureBlockTranslation />
					</BentoItem>

					<BentoItem
						title="Speaker Recognition"
						description="Never confuse “who said what” again. Our advanced speaker recognition technology pinpoints each voice in your M4A files, labeling speakers clearly in your transcripts. Whether you have two participants or ten, every contribution is captured and organized—making your notes easy to follow and your workflow more efficient."
						className="col-span-1 border md:col-span-4"
					>
						<FeatureBlockSpeaker />
					</BentoItem>

					<BentoItem
						title="Variety of formats"
						description="It’s more than M4A to text—transcribe audio and video files of any format. From MP3, WAV, and AAC to MP4 and MOV, our tool handles them all effortlessly. Upload any file type, and get back an accurate, organized, and searchable transcript every single time."
						className="col-span-1 border md:col-span-5"
					>
						{/* <FeatureBlockFileFormats /> */}
						<div className="mt-8 flex flex-row flex-wrap items-center gap-4">
							<CustomIconsMP3 />
							<CustomIconsMAV />
							<CustomIconsAAC />
							<CustomIconsM4A />
							<CustomIconsMP4 />
							<CustomIconsMOV />
							<CustomIconsAVI />
						</div>
					</BentoItem>
				</div>
			</div>

			<GridSections
				title="Features of M4A to Text Transcription"
				sections={[
					{
						title: "Ultra-Fast",
						description: "Experience transcription that finishes in seconds, not hours.",
						icon: Zap,
					},
					{
						title: "High Accuracy",
						description: "Enjoy 99.8% word-for-word accuracy on every transcript.",
						icon: Gem,
					},
					{
						title: "Speaker Detection",
						description: "Identify and label multiple speakers automatically.",
						icon: AudioLines, // 'voice' mapped to AudioLines import
					},
					{
						title: "Unlimited Transcriptions",
						description: "Transcribe as many files as you want—no limits, ever.",
						icon: Infinity,
					},
					{
						title: "Private & Secure",
						description: "Your files and texts stay safe with end-to-end encryption.",
						icon: ShieldCheck,
					},
					{
						title: "Simple Interface",
						description: "Effortless upload and download—no learning curve required.",
						icon: Smile, // 'settings' mapped to Smile import for simplicity
					},
				]}
			/>

			<FAQsComponent
				title="M4A to Text Transcription Related FAQs"
				faqs={[
					{
						question: "What is Unoscribe online AI transcription tool?",
						answer: "Unoscribe is a powerful yet simple online AI transcription tool that helps you effortlessly convert audio and video files—including M4A—into highly accurate text. With Unoscribe, there’s no need to download software or learn complicated processes. Simply upload your file and let our advanced AI technology handle the transcription with remarkable speed and accuracy. Whether you’re a student, journalist, business professional, or content creator, Unoscribe makes turning spoken words into written text quick, secure, and stress-free.",
					},
					{
						question: "Can you transcribe M4A to text?",
						answer: "Absolutely! Unoscribe is expertly built to transcribe M4A to text online—simply upload your M4A file, and our advanced AI will convert it to easy-to-read text in seconds. No hassle, no complicated steps—just instant and reliable M4A transcription whenever you need it.",
					},
					{
						question: "Can I try the M4A to text transcription for free?",
						answer: "Yes, you can! Unoscribe offers a free trial so you can experience how simple and effective it is to transcribe M4A to text online. Test the features, check the accuracy, and see how it fits into your workflow before you commit. Our goal is to help you, risk-free.",
					},
					{
						question: "How accurate is the M4A transcription?",
						answer: "Accuracy is our promise. Unoscribe delivers up to 99.8% transcription accuracy for clear audio. This means you get reliable, trustworthy text with minimal errors. No more worrying about missing words or confusing transcripts—your M4A files will be transcribed with incredible precision.",
					},
					{
						question: "In what formats can I export my transcribed text?",
						answer: "Unoscribe gives you maximum flexibility! You can export your transcribed M4A text in a wide variety of popular formats, including TXT, PDF, DOCX, SRT (for subtitles), and MD (markdown). With just one click, you have your transcript ready for editing, sharing, subtitling, or archiving.",
					},
					{
						question: "Which languages are supported for M4A transcription?",
						answer: "Unoscribe supports 100+ languages worldwide—so whether your M4A file is in English, Chinese, Spanish, French, German, Japanese, Russian, or many more, Unoscribe is always ready to transcribe M4A to text accurately for you. We believe in breaking language barriers!",
					},
					{
						question: "Can I get subtitles from my M4A files?",
						answer: "Yes! With Unoscribe, you can easily generate and download subtitles (SRT files) directly from your M4A audio. This is perfect for adding captions to your videos, boosting accessibility, or sharing accurate subtitles with your audience.",
					},
					{
						question: "Is my data safe and private with Unoscribe?",
						answer: "Your privacy is our top priority. All your uploaded M4A files and transcribed text are protected by strong security measures. We never share or sell your data, and everything remains private. You can confidently transcribe sensitive content with peace of mind.",
					},
					{
						question: "Can Unoscribe recognize different speakers in my M4A recordings?",
						answer: "Yes, Unoscribe features advanced speaker identification. Our AI automatically distinguishes and labels different speakers within your M4A file, making your transcript easy to read and understand—even for interviews, meetings, and group discussions.",
					},
				]}
			/>

			<FinalCTA
				title="Start Converting M4A to Text Instantly"
				description="Experience how easy it is to transcribe M4A to text—accurate results delivered in seconds. Free yourself from time-consuming transcription and enjoy organized, searchable text for every audio file."
				ctaText="Transcribe M4A to Text Free"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
			/>
		</main>
	);
}
