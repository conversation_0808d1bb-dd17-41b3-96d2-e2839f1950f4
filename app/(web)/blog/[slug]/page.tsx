import { i18nConfig } from "@/i18n-config";
import { notFound } from "next/navigation";
// import remarkGfm from "remark-gfm";
// import remarkMath from "remark-math";
// import { MemoizedReactMarkdown } from "@/components/markdown";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import TocComponent from "./toc-component";
import { WEBNAME } from "@/lib/constants";
import FinalCTA from "@/components/landing/final-cta";
import { getBlogWithCategory } from "@/server/utils-blog.server";

type Params = Promise<{ slug: string }>;

export const generateMetadata = async ({ params }: { params: Params }) => {
	const { slug } = await params;
	const lang = "en";
	const { blog } = await getBlogWithCategory(lang, slug);
	let prefixLang = "";
	if (lang && lang !== i18nConfig.defaultLocale) {
		prefixLang = `/${lang}`;
	}
	const canonicalPath = `${prefixLang}/blog/${slug}`;
	if (!blog) {
		return {
			title: `${WEBNAME} Blog`,
			description: "",
			alternates: {
				canonical: canonicalPath,
			},
		};
	}
	return {
		title: `${blog.metaTitle ?? blog.title} | ${WEBNAME} Blog`,
		description: blog.metaDescription ?? blog.intro,
		alternates: {
			canonical: canonicalPath,
		},
	};
};

export default async function Page({ params }: { params: Params }) {
	const { slug } = await params;
	const lang = "en";
	const { blog, blogCategories } = await getBlogWithCategory(lang, slug);
	if (!blog) {
		return notFound();
	}
	let categoryName: string | null = null;
	if (blog.categoryId) {
		categoryName = blogCategories.find((category: any) => category.id === blog.categoryId)?.name;
	}
	// const toc = extractTableOfContents(blog.markdown);
	// const toc = extractTableOfContentsFromHtml(blog.html);
	// console.log("toc:", toc);

	// const hasToc = blog.html.includes("<h2");

	return (
		<main className="flex w-full flex-col">
			<div className="container flex flex-col gap-6 pb-12 pt-12">
				<Breadcrumb>
					<BreadcrumbList className="text-base">
						<BreadcrumbItem>
							<BreadcrumbLink href="/" className="text-sm hover:underline">
								Home
							</BreadcrumbLink>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<BreadcrumbLink href="/blog" className="text-sm hover:underline">
								Blog
							</BreadcrumbLink>
						</BreadcrumbItem>
						{/* <BreadcrumbItem>
							<BreadcrumbPage className="max-w-[300px] truncate text-sm text-blue-900">{blog.title}</BreadcrumbPage>
						</BreadcrumbItem> */}
					</BreadcrumbList>
				</Breadcrumb>
				<div className="flex flex-col gap-4 md:gap-6">
					<h1 className="text-pretty text-4xl font-bold text-primary">{blog.title}</h1>
					<p className="text-muted-foreground">{blog.intro}</p>
					<div className="flex items-center space-x-2 text-sm text-gray-500">
						<span className="text-xs">{format(blog.publishedAt, "MMM d, yyyy")}</span>
						{categoryName && (
							<div className="flex flex-wrap gap-2">
								<Badge className="rounded-full font-normal shadow-none">{categoryName}</Badge>
							</div>
						)}
					</div>
				</div>
			</div>

			<div className="container flex space-x-16 pb-20 lg:space-x-20">
				<div className="w-full">
					{/* <div className={cn("w-full md:pr-8", hasToc && "md:w-3/4")}> */}
					<div
						className="prose-headings:font-title font-default prose max-w-full dark:prose-invert focus:outline-hidden"
						dangerouslySetInnerHTML={{ __html: blog.html ?? "" }}
					/>
					{/* <MemoizedReactMarkdown
						className="prose max-w-none break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0"
						remarkPlugins={[remarkGfm, remarkMath]}
						components={{
							p({ children }) {
								return <p className="mb-6 last:mb-0">{children}</p>;
							},
							h1: ({ children }) => (
								<h1 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h1>
							),
							h2: ({ children }) => (
								<h2 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h2>
							),
							h3: ({ children }) => (
								<h3 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h3>
							),
							h4: ({ children }) => (
								<h4 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h4>
							),
							h5: ({ children }) => (
								<h5 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h5>
							),
							h6: ({ children }) => (
								<h6 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h6>
							),
						}}
					>
						{blog.markdown}
					</MemoizedReactMarkdown> */}
				</div>
				{/* {toc.length > 0 && (
					<div className="hidden md:block md:w-1/4">
						<div className="sticky top-20">
							<div className="rounded-lg bg-gray-50 p-6 shadow-md">
								<h2 className="mb-3 text-lg font-semibold text-blue-900">Table of Contents</h2>
								<nav>
									<ul className="space-y-1">
										{toc.map((item, index) => (
											<li
												key={index}
												className={`transition-all duration-200 ease-in-out ${item.level > 1 ? `pl-${(item.level - 2) * 4}` : ""}`}
											>
												<a href={`#${item.id}`} className="block py-1 text-sm text-gray-700 hover:underline">
													{item.title}
												</a>
											</li>
										))}
									</ul>
								</nav>
							</div>
						</div>
					</div>
				)} */}
				<TocComponent html={blog.html!} />
			</div>

			{/* <FinalCTA /> */}
		</main>
	);
}
