import { WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import BlogItems from "./blog-items";
import FinalCTA from "@/components/landing/final-cta";
import { getBlogHeadsWithCategory } from "@/server/utils-blog.server";

export const metadata: Metadata = {
	title: `${WEBNAME} Blog: Get Better at Portrait Editing & Photo Styling`,
	description: "",
	alternates: {
		canonical: "/blog",
	},
};

export default async function Page() {
	const { blogHeads, blogCategories } = await getBlogHeadsWithCategory(1);
	return (
		<main className="min-h-screen pb-16">
			<div className="container pb-16 pt-12 md:max-w-4xl">
				<div className="flex flex-col gap-2">
					<h1 className="mt-8 text-pretty text-base font-medium text-primary text-teal-500">{WEBNAME} Blog</h1>
					<p className="text-balance text-4xl font-semibold text-primary">News, insights and more</p>
				</div>
			</div>

			<div className="container md:max-w-4xl">
				<BlogItems blogHeads={blogHeads} blogCategories={blogCategories} />
			</div>
		</main>
	);
}
