import { OSS_URL, WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import { AudioLines, Zap, Gem, FileText, ShieldCheck, Infinity, Smile } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { HowToUse } from "@/components/landing/how-to-use";
import { BentoItem } from "@/components/landing/features-bento";
import { FeatureBlockTranslation, FeatureBlockSpeaker } from "@/components/landing/feature-block-translation";
import {
	CustomIconsAAC,
	CustomIconsAVI,
	CustomIconsM4A,
	CustomIconsMAV,
	CustomIconsMOV,
	CustomIconsMP3,
	CustomIconsMP4,
	IconsDocument,
	IconsMic,
} from "@/components/icons";
import TranscribeClient from "@/components/app/transcribe.client";
import ConvertClient from "./convert.client";

export const metadata: Metadata = {
	title: `M4A to MP3    | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/m4a-to-mp3",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<section>
				<div className="relative pt-12 pb-12">
					<div className="mx-auto max-w-4xl px-6">
						<div className="mt-8 text-center lg:mt-16">
							<h1 className="mx-auto max-w-3xl text-3xl font-semibold sm:text-4xl">Convert M4A to MP3</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								<p></p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container w-full px-4 pb-36">
				<ConvertClient   />
			</div>
		</main>
	);
}
