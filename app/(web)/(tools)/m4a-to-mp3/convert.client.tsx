"use client";

import React, { useRef } from "react";
import { useState } from "react";
import { Upload, ArrowR<PERSON>, CircleXIcon } from "lucide-react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import loadFfmpeg from "@/lib/utils-load-ffmpeg";
import { fetchFile } from "@ffmpeg/util";
import { toast } from "sonner";
import { Dropzone, DropzoneContent, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/custom/progress";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getFileName } from "@/lib/file/utils-file";
import { SubmitButton } from "@/components/ui/submit-button";
import { FILE_SIZE_LIMIT_MAX } from "@/lib/constants";

export default function ConvertClient() {
	const ffmpegRef = useRef<any>(null);
	// const [isLoading, setIsLoading] = useState<boolean>(false);
	// const load = async () => {
	// 	try {
	// 		setIsLoading(true);
	// 		const ffmpeg_response: FFmpeg = await loadFfmpeg();
	// 		ffmpegRef.current = ffmpeg_response;
	// 	} catch (error) {
	// 		console.error("Failed to load FFmpeg:", error);
	// 		toast.error("Failed to initialize video processor");
	// 	} finally {
	// 		setIsLoading(false);
	// 	}
	// };
	// useEffect(() => {
	// 	load();
	// }, []);

	const [selectFileName, setSelectFileName] = useState<string | null>(null);
	const [selectFile, setSelectFile] = useState<File | null>(null);

	const [converting, setConverting] = useState<boolean>(false);
	const [convertFileProgress, setConvertFileProgress] = useState<number>(0);
	const [convertedFile, setConvertedFile] = useState<File | null>(null);

	const convertToMp3 = async (file: File, fileName: string): Promise<File> => {
		const ffmpeg = ffmpegRef.current;
		// get the original file name
		const originalFileName = file.name;
		// write the file to the file system
		await ffmpeg.writeFile(originalFileName, await fetchFile(file));
		// transcode the file
		await ffmpeg.exec(["-i", originalFileName, "-f", `mp3`, "-vn", `${fileName}.mp3`]);
		const data = await ffmpeg.readFile(`${fileName}.mp3`);
		return new File([new Blob([data.buffer], { type: "audio/*" })], `${fileName}.mp3`, { type: "audio/mpeg" });
	};
	const handleLocalFileDrop = async (files: File[]) => {
		if (!files || files.length === 0) return;
		let file = files[0];

		if (file.size > FILE_SIZE_LIMIT_MAX) {
			toast.error("File size greater than 2GB, please upload a smaller file.");
			return;
		}

		const fileName = getFileName(file.name);
		setSelectFile(file);
		setSelectFileName(fileName);
	};
	const handleConvert = async () => {
		if (converting) return;
		if (!selectFile) return;

		try {
			setConverting(true);
			setConvertFileProgress(0);
			if (!ffmpegRef.current) {
				try {
					const ffmpeg_response: FFmpeg = await loadFfmpeg(setConvertFileProgress);
					ffmpegRef.current = ffmpeg_response;
				} catch (error) {
					console.error("Failed to load FFmpeg:", error);
					toast.error("Failed to initialize video processor");
				}
			}
			const file = await convertToMp3(selectFile, selectFileName!);
			setConvertedFile(file);
		} catch (error: any) {
			console.error("Failed to convert file:", error.message);
			toast.error(`Failed to convert file: ${error.message}`);
		} finally {
			setConverting(false);
		}
	};

	const handleDownload = async () => {
		if (converting) return;
		if (!convertedFile) return;

		//download file
		const url = URL.createObjectURL(convertedFile);
		const link = document.createElement("a");
		link.href = url;
		link.download = `${selectFileName}.mp3`;
		link.click();
	};

	return (
		<div className={cn("bg-muted mx-auto max-w-[800px] rounded-2xl shadow-lg")}>
			{selectFile || converting ? (
				<div className="flex h-full w-full flex-col gap-2 rounded-2xl border p-2">
					<div className="overflow-hidden rounded-xl border">
						<Table>
							<TableHeader className="[&_tr]:border-b-0">
								<TableRow className="bg-zinc-50 hover:bg-zinc-50">
									<TableHead className="text-xs font-medium text-zinc-500">File</TableHead>
									<TableHead className="text-xs font-medium text-zinc-500">Output</TableHead>
									<TableHead className="text-xs font-medium text-zinc-500"></TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								<TableRow className="bg-white hover:bg-white">
									<TableCell className="flex w-full flex-row items-center gap-1">
										<span className="line-clamp-2 font-[380] text-wrap text-zinc-800">{selectFile?.name}</span>
									</TableCell>
									<TableCell className="text-muted-foreground mx-auto md:w-[20px]">
										<Button size="sm" variant="ghost" className={cn("text-zinc-600", converting ? "cursor-not-allowed" : "cursor-pointer")}>
											MP3
										</Button>
									</TableCell>
									<TableCell className="text-muted-foreground mx-auto md:w-[20px]">
										<Button
											size="icon"
											variant="ghost"
											className={cn("text-zinc-600", converting ? "cursor-not-allowed" : "cursor-pointer")}
											onClick={() => {
												if (!converting) {
													setSelectFile(null);
													setConvertedFile(null);
												}
											}}
										>
											<CircleXIcon className="size-5" />
										</Button>
									</TableCell>
								</TableRow>
							</TableBody>
						</Table>
					</div>
					<div className="mx-auto flex flex-row items-center gap-2">
						{(converting || convertedFile) && (
							<>
								{converting ? (
									<div className="flex h-full flex-col gap-0.5">
										<p className="text-sm text-zinc-500">
											Converting ... <span className="font-mono text-green-500 tabular-nums">{convertFileProgress}%</span>
										</p>
										<Progress value={convertFileProgress} className="h-1.5 max-w-[128px] bg-zinc-200" indicatorClassName="bg-green-500" />
									</div>
								) : (
									<p className="rounded-md border border-green-500 px-2 py-1 text-sm text-green-500">Done</p>
								)}
								<Button
									size="lg"
									variant="default"
									disabled={converting || !convertedFile}
									className="cursor-pointer bg-blue-500 hover:bg-blue-500"
									onClick={handleDownload}
								>
									Download
								</Button>
							</>
						)}
						{!convertedFile && !converting && (
							<SubmitButton
								size="lg"
								isSubmitting={converting}
								variant="default"
								disabled={converting || !selectFile}
								className="cursor-pointer bg-blue-500 hover:bg-blue-500"
								onClick={handleConvert}
							>
								Convert
								<ArrowRight />
							</SubmitButton>
						)}
					</div>
				</div>
			) : (
				<Dropzone
					multiple={false}
					maxFiles={1}
					onDrop={handleLocalFileDrop}
					accept={{
						"audio/mpeg": [],
						"audio/wav": [],
						"audio/aac": [],
						"audio/mp4": [],
						"audio/x-ms-wma": [],
						"audio/ogg": [],
						"audio/flac": [],
						"video/*": [],
					}}
					onError={console.error}
					className={cn(
						"hover:border-primary h-full min-h-[256px] cursor-pointer rounded-2xl border-2 border-dashed border-zinc-400 bg-blue-50 whitespace-pre-wrap hover:bg-blue-50",
					)}
				>
					<DropzoneEmptyState>
						<>
							<div className="full font-norma mt-4 space-y-2 text-sm">
								<p className={cn(buttonVariants({ size: "lg" }), "mt-2 h-12 bg-blue-600 hover:bg-blue-600/90 has-[>svg]:px-8")}>
									<Upload className="" />
									Choose a file
								</p>
								<p className="w-full text-sm font-normal text-zinc-800">Max file size 2GB</p>
							</div>
						</>
					</DropzoneEmptyState>
					<DropzoneContent />
				</Dropzone>
			)}
		</div>
	);
}
