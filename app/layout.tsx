import "./globals.css";
import { fontHeading, fontSans } from "@/lib/fonts";
import { Toaster } from "@/components/ui/sonner";
import { CookiesProvider } from "next-client-cookies/server";
import NextTopLoader from "nextjs-toploader";
// import { ThemeProvider } from "@/components/provider/theme-provider"
import { Metadata } from "next";
import { WEB_URL, WEBNAME } from "@/lib/constants";

export const metadata: Metadata = {
	metadataBase: new URL(WEB_URL),
	openGraph: {
		title: `${WEBNAME} - Transcribe Audio and Video to Accurate Text`,
		type: "website",
		url: WEB_URL,
		// description: "",
		// images: `${OSS_URL_HOST}/mkt/og-image.webp`,
	},
	twitter: {
		site: WEB_URL,
	},
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en">
			<head>
				<link rel="icon" href="/favicon.ico" sizes="any" />
			</head>

			<body className={`${fontSans.variable} font-sans ${fontHeading.variable}`}>
				{/* <ThemeProvider
						attribute="class"
						defaultTheme="dark"
						enableSystem
						disableTransitionOnChange
					> */}
				<NextTopLoader color="#3b82f6" initialPosition={0.3} speed={600} crawlSpeed={200} showSpinner={false} shadow={false} />
				<CookiesProvider>
					{children}
					<Toaster richColors position="top-center" />
				</CookiesProvider>
				{/* </ThemeProvider> */}
			</body>
		</html>
	);
}
