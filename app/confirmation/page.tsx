import { buttonVariants } from "@/components/ui/button";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { WEBNAME } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { Metadata } from "next";

export const metadata: Metadata = {
	title: `Confirmation | ${WEBNAME}`,
	alternates: {
		canonical: "/confirmation",
	},
	robots: {
		index: false,
		follow: false,
	},
};
export default function Page() {
	return (
		<div className="flex h-screen items-center justify-center p-6">
			<div className="w-full max-w-md space-y-8 rounded-2xl border bg-white p-8 shadow-xl transition-all duration-300 hover:shadow-blue-100">
				<div className="flex flex-col items-center">
					<div className="mt-4 text-center text-gray-600">
						<p className="text-lg">Thank you! Your checkout is now being processed.</p>
						<p className="mt-2 text-sm text-gray-500">Please wait a moment, it may take a few seconds or minutes.</p>
					</div>
				</div>

				<div className="pt-4">
					<NoPrefetchLink
						href={`/user/my-subscriptions?t=${Date.now()}`}
						className={cn(
							buttonVariants({ variant: "default" }),
							"w-full bg-linear-to-r from-blue-500 to-blue-600 py-2 font-medium text-white transition-all duration-300 hover:from-blue-600 hover:to-blue-700 hover:shadow-md",
						)}
					>
						Go to my billing
					</NoPrefetchLink>
				</div>

				<div className="flex justify-center">
					<div className="mt-2 animate-bounce text-xs text-gray-400">✨ Your journey with us begins now ✨</div>
				</div>
			</div>
		</div>
	);
}
