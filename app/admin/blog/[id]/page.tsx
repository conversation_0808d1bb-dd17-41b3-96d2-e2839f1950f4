import { notFound } from "next/navigation";
import { blogItemSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc, eq } from "drizzle-orm";
import { BlogSchemaType } from "@/@types/admin/blog/blog";
import EditBlogComponent from "../edit-blog-component";

async function getBlogs(id: number) {
	const db = getDB();
	const blogs: BlogSchemaType[] = (await db
		.select()
		.from(blogItemSchema)
		.where(eq(blogItemSchema.id, id))
		.orderBy(desc(blogItemSchema.id))) as BlogSchemaType[];
	return blogs[0];
}

type Params = Promise<{ id: number }>;
export default async function Page({ params }: { params: Params }) {
	const { id } = await params;
	const blog = await getBlogs(id);
	if (!blog) {
		return notFound();
	}
	return <EditBlogComponent blogId={blog!.id} blog={blog} />;
}
