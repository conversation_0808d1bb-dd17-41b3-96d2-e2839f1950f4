import { notFound } from "next/navigation";
import { blogItemSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc } from "drizzle-orm";
import BlogHeadsComponent from "./blog-heads.client";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";

async function getBlogHeads() {
	if (!checkAuthAdmin()) {
		notFound();
	}

	const db = getDB();
	const blogHeads: any[] = await db
		.select({
			id: blogItemSchema.id,
			slug: blogItemSchema.slug,
			categoryId: blogItemSchema.categoryId,
			lang: blogItemSchema.lang,
			title: blogItemSchema.title,
			status: blogItemSchema.status,
			publishedAt: blogItemSchema.publishedAt,
		})
		.from(blogItemSchema)
		.orderBy(desc(blogItemSchema.id));

	return blogHeads;
}

export default async function Page() {
	const blogHeads = await getBlogHeads();

	return <BlogHeadsComponent blogHeads={blogHeads} />;
}
