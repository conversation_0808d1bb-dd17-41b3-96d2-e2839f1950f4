"use client";

import { buttonVariants } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { useRouter } from "nextjs-toploader/app";
import { format } from "date-fns";
import { DashHeader } from "../_components/admin-navigate";
import { BlogStatus } from "@/@types/admin/blog/blog";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default function BlogHeadsComponent({ blogHeads }: { blogHeads: any[] }) {
	const router = useRouter();
	const { categories } = useBlogCategoryStore();

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader back={[{ href: "/admin", title: "Admin" }]} current={{ title: "Blogs" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 pt-4 text-start md:container">
					<div className="flex w-full flex-row justify-end">
						<NoPrefetchLink href="/admin/blog/new" className={cn(buttonVariants({ variant: "outline" }), "cursor-pointer")}>
							<Plus className="mr-1 h-4 w-4" />
							New
						</NoPrefetchLink>
					</div>

					<Table>
						<TableHeader className="[&_tr]:border-b-0">
							<TableRow className="bg-muted">
								<TableHead className="font-normal"></TableHead>
								<TableHead className="font-normal">Name</TableHead>
								<TableHead className="font-normal">Language</TableHead>
								<TableHead className="font-normal">Category</TableHead>
								<TableHead className="font-normal">Published Date</TableHead>
								<TableHead className="font-normal">Status</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{blogHeads.map((blogHead, index) => (
								<TableRow
									key={blogHead.id}
									className="cursor-pointer"
									onClick={() => {
										router.push(`/admin/blog/${blogHead.id}?_=${new Date().getTime()}`);
									}}
								>
									<TableCell className="text-muted-foreground w-8 py-4">
										<span className="text-sm">{index + 1}</span>
									</TableCell>
									<TableCell className="font-medium text-gray-800">
										<span className="text-sm">{blogHead.title}</span>
									</TableCell>
									<TableCell className="font-medium text-gray-800">
										<span className="text-sm">{blogHead.lang}</span>
									</TableCell>
									<TableCell className="mx-auto">
										{blogHead.categoryId && (
											<Badge variant="secondary" className="rounded-full font-normal">
												{categories.find((category) => category.id === blogHead.categoryId)?.name}
											</Badge>
										)}
									</TableCell>
									<TableCell className="text-gray-800">
										<span className="text-sm">{blogHead.publishedAt ? format(blogHead.publishedAt, "MMM d, yyyy") : ""}</span>
									</TableCell>
									<TableCell className="mx-auto">
										{blogHead.status === BlogStatus.Published && (
											<Badge variant="secondary" className="rounded-full bg-green-50 font-normal text-green-600 hover:bg-green-50">
												Published
											</Badge>
										)}
										{blogHead.status === BlogStatus.Draft && (
											<Badge variant="secondary" className="rounded-full font-normal">
												Draft
											</Badge>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>
		</div>
	);
}
