import { notFound } from "next/navigation";
import { changelogItemSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc } from "drizzle-orm";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import ChangelogHeadsComponent from "./changelog-heads.client";

async function getChangelogHeads() {
	if (!checkAuthAdmin()) {
		notFound();
	}

	const db = getDB();
	const changelogHeads: any[] = await db
		.select({
			id: changelogItemSchema.id,
			lang: changelogItemSchema.lang,
			majorVersion: changelogItemSchema.majorVersion,
			minorVersion: changelogItemSchema.minorVersion,
			patchVersion: changelogItemSchema.patchVersion,
			title: changelogItemSchema.title,
			status: changelogItemSchema.status,
			publishedAt: changelogItemSchema.publishedAt,
		})
		.from(changelogItemSchema)
		.orderBy(desc(changelogItemSchema.id));

	return changelogHeads;
}

export default async function Page() {
	const changelogHeads = await getChangelogHeads();

	return <ChangelogHeadsComponent changelogHeads={changelogHeads} />;
}
