import { notFound } from "next/navigation";
import { changelogItemSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc, eq } from "drizzle-orm";
import EditChangelogComponent from "../edit-changelog-component";
import { ChangelogSchemaType } from "@/@types/admin/changelog/changelog";

async function getChangelog(id: number) {
	const db = getDB();
	const changelogs: ChangelogSchemaType[] = (await db
		.select()
		.from(changelogItemSchema)
		.where(eq(changelogItemSchema.id, id))
		.orderBy(desc(changelogItemSchema.id))) as ChangelogSchemaType[];
	return changelogs[0];
}

type Params = Promise<{ id: number }>;
export default async function Page({ params }: { params: Params }) {
	const { id } = await params;
	const changelog = await getChangelog(id);
	if (!changelog) {
		return notFound();
	}
	return <EditChangelogComponent changelogId={changelog!.id} changelog={changelog} />;
}
