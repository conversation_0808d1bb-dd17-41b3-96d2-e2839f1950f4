"use client";

import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, ChevronLeft, Dot, Send } from "lucide-react";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/submit-button";
import { ofetch } from "ofetch";
import { handleError, ParamsError } from "@/@types/error";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import useAutosizeTextArea from "@/hooks/useAutosizeTextArea";
import Editor from "@/components/novel/advanced-editor";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "nextjs-toploader/app";
import { JSONContent } from "novel";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import { Bold } from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Heading from "@tiptap/extension-heading";
import { Link as TipTapLink } from "@tiptap/extension-link";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import { Color } from "@tiptap/extension-color";
import TaskItem from "@tiptap/extension-task-item";
import TaskList from "@tiptap/extension-task-list";
import OrderedList from "@tiptap/extension-ordered-list";
import Image from "@tiptap/extension-image";
import CodeBlock from "@tiptap/extension-code-block";
import Blockquote from "@tiptap/extension-blockquote";
import { generateJSON } from "@tiptap/html";
import { ChangelogSchemaType, getChangelogStatusText } from "@/@types/admin/changelog/changelog";
import { Input } from "@/components/ui/input";
import { WEB_URL } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default function EditChangelogComponent({
	changelogId,
	changelog,
}: {
	changelogId: number | null | undefined;
	changelog: ChangelogSchemaType | null | undefined;
}) {
	const router = useRouter();

	const [isSubmitting, setIsSubmitting] = useState(false);

	const [majorVersion, setMajorVersion] = useState<string>("");
	const [minorVersion, setMinorVersion] = useState<string>("");
	const [patchVersion, setPatchVersion] = useState<string>("");
	const [title, setTitle] = useState<string>("");
	const titleTextAreaRef = useRef<HTMLTextAreaElement>(null);
	useAutosizeTextArea(titleTextAreaRef.current, title);

	const [languageCode, setLanguageCode] = useState<string>("en");
	// const [featuredImageUrl, setFeaturedImageUrl] = useState<string>("");
	const [publishedAt, setPublishedAt] = useState<Date | undefined>(new Date());
	const [changelogStatus, setChangelogStatus] = useState<string>("0");
	const [changelogStatusServer, setChangelogStatusServer] = useState<string>("");

	const [html, setHtml] = useState<string>();
	// const [initialHtmlJson, setInitialHtmlJson] = useState<JSONContent>();
	const [initialHtmlJson, setInitialHtmlJson] = useState<JSONContent | undefined>(
		changelog?.html
			? generateJSON(changelog.html, [
					Document,
					Bold,
					Heading,
					TipTapLink,
					Paragraph,
					BulletList,
					OrderedList,
					ListItem,
					TaskList,
					TaskItem,
					Text,
					TextStyle,
					Color,
					Image,
					CodeBlock,
					Blockquote,
				])
			: undefined,
	);

	useEffect(() => {
		if (changelog) {
			setMajorVersion(changelog.majorVersion.toString());
			setMinorVersion(changelog.minorVersion.toString());
			setPatchVersion(changelog.patchVersion.toString());
			setTitle(changelog.title);
			setLanguageCode(changelog.lang);
			// setImage(changelog.image);
			setHtml(changelog.html);
			setPublishedAt(changelog.publishedAt);
			setChangelogStatus(changelog.status.toString());
			setChangelogStatusServer(changelog.status.toString());

			// if (changelog.html) {
			// 	setInitialHtmlJson(generateJSON(changelog.html, [Bold, BulletList, Document, Heading, TipTapLink, Paragraph, ListItem, Text, TextStyle, Color]));
			// }
		}
	}, []);

	const onSubmit = async () => {
		if (isSubmitting) return;

		const titleTrim = title.trim();

		if (majorVersion === "" || minorVersion === "" || patchVersion === "") {
			toast.info("Please fill version");
			return;
		}

		if (!titleTrim || !languageCode) {
			toast.info("Please fill in all fields");
			return;
		}

		try {
			setIsSubmitting(true);

			let requestBody: any = {
				title: titleTrim,
				lang: languageCode,
				// image: featuredImageUrl,
				html: html,
				publishedAt: publishedAt ?? new Date(),
				status: Number(changelogStatus),
				majorVersion: Number(majorVersion),
				minorVersion: Number(minorVersion),
				patchVersion: Number(patchVersion),
			};
			if (changelogId) requestBody.id = changelogId;

			const { status, message, newChangelogId } = await ofetch("/api/admin/changelog/changelog-item", {
				method: "POST",
				body: requestBody,
			});
			handleError(status, message);
			if (changelogId) {
				setChangelogStatusServer(changelogStatus);
				toast.success("Changelog updated successfully");
				setIsSubmitting(false);
			} else {
				toast.success("Changelog added successfully");
				if (newChangelogId) {
					router.push(`/admin/changelog/${newChangelogId}`);
				}
			}
		} catch (error) {
			if (error instanceof ParamsError) {
				toast.error(error.message);
			} else {
				toast.error("An error occurred");
			}
			setIsSubmitting(false);
		}
	};

	return (
		<div className="flex h-full w-full flex-1 flex-row">
			<div className="flex h-full w-full flex-col overflow-hidden border-r">
				<div className="flex w-full flex-row items-center justify-between p-4">
					<div className="flex flex-row items-center">
						<NoPrefetchLink href="/admin/changelog" className={cn("", "flex flex-row items-center text-sm font-normal")}>
							<ChevronLeft className="mr-1 h-4 w-4" />
							Changelog
						</NoPrefetchLink>
						<p className={cn("flex flex-row items-center text-xs", changelogStatusServer === "1" ? "text-green-500" : "text-muted-foreground")}>
							<Dot />
							<span>{getChangelogStatusText(Number(changelogStatusServer))}</span>
						</p>
					</div>
					<SubmitButton isSubmitting={isSubmitting} size="sm" onClick={onSubmit}>
						<p className="flex flex-row items-center gap-1">
							<Send />
							<span>Save</span>
						</p>
					</SubmitButton>
				</div>

				<div className="mx-auto w-full max-w-3xl px-4">
					<div className="flex w-full flex-col space-y-4">
						<div className="">
							<Textarea
								ref={titleTextAreaRef}
								value={title}
								rows={1}
								className="min-h-[40px] resize-none rounded-none border-none p-0 text-2xl font-semibold shadow-none focus-visible:ring-0 focus-visible:outline-hidden"
								onChange={(e) => {
									setTitle(e.target.value);
									if (changelogId) return;
								}}
								placeholder="Title"
							/>
						</div>
						<Separator className="" />
					</div>
				</div>

				<div className="h-full grow overflow-hidden">
					<ScrollArea className="h-full w-full" type="always">
						<div className="mx-auto max-w-3xl px-4 py-4">
							{/* <Editor initialHtml={html} onChange={setHtml} /> */}
							<Editor initialHtmlJson={initialHtmlJson} onChange={setHtml} />
						</div>
					</ScrollArea>
				</div>
			</div>

			<div className="h-full w-[360px] py-4 lg:w-[420px]">
				<Label className="flex-1 px-4 text-base">Changelog settings</Label>
				<div className="h-full grow overflow-hidden pb-2">
					<ScrollArea className="h-full w-full" type="always">
						<div className="my-4 space-y-6 px-4">
							{changelogId && (
								<div className="flex flex-col items-start gap-2 rounded-lg bg-blue-100 p-3">
									<NoPrefetchLink href={`${WEB_URL}/changelog`} target="_blank" className="text-xs underline">
										{WEB_URL}/changelog
									</NoPrefetchLink>
								</div>
							)}

							<div className="flex flex-col items-start gap-2">
								<Label>Version *</Label>
								<div className="flex flex-row items-center gap-1">
									<p className="text-muted-foreground">v</p>
									<Input
										value={majorVersion}
										type="number"
										min={1}
										onChange={(e) => setMajorVersion(e.target.value)}
										placeholder="1"
										className=""
									/>
									<p className="text-muted-foreground">.</p>
									<Input
										value={minorVersion}
										type="number"
										min={0}
										onChange={(e) => setMinorVersion(e.target.value)}
										placeholder="0"
										className=""
									/>
									<p className="text-muted-foreground">.</p>
									<Input
										value={patchVersion}
										type="number"
										min={0}
										onChange={(e) => setPatchVersion(e.target.value)}
										placeholder="0"
										className=""
									/>
								</div>
							</div>
							<div className="flex flex-col items-start gap-2">
								<Label>Language *</Label>
								<Select value={languageCode} onValueChange={(value) => setLanguageCode(value as string)}>
									<SelectTrigger id="framework">
										<SelectValue placeholder="Select Language">English</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="en" className="cursor-pointer">
											English
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
							{/* <div className="flex flex-col items-start gap-2">
								<Label className="">Featured Image</Label>
								<Input value={title || ""} onChange={(e) => setTitle(e.target.value)} placeholder="Image URL" className="shrink-0" />
							</div> */}
							<div className="flex flex-col items-start gap-2">
								<Label className="">Date published * </Label>
								<div className="flex w-full items-center justify-between gap-1">
									<Popover modal={true}>
										<PopoverTrigger asChild>
											<Button variant={"outline"} className={cn("w-full font-normal", !publishedAt && "text-muted-foreground")}>
												<CalendarIcon />
												{publishedAt ? format(publishedAt, "P") : <span>Pick a date</span>}
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar
												mode="single"
												selected={publishedAt ? publishedAt : undefined}
												onSelect={(date) => {
													setPublishedAt(date);
												}}
												disabled={(date) => date < new Date("1900-01-01")}
											/>
										</PopoverContent>
									</Popover>
									{/* {publishedAt ? (
										<Button variant="ghost" size="icon" className="shrink-0" onClick={() => setPublishedAt(undefined)}>
											<CircleX />
										</Button>
									) : (
										<div className="h-9 w-9 shrink-0" />
									)} */}
								</div>
							</div>
							<div className="flex flex-col items-start gap-2">
								<Label>Status</Label>
								<Select value={changelogStatus} onValueChange={(value) => setChangelogStatus(value as string)}>
									<SelectTrigger>
										<SelectValue placeholder="Select Category">{getChangelogStatusText(Number(changelogStatus))}</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="0" className="cursor-pointer">
											Draft
										</SelectItem>
										<SelectItem value="1" className="cursor-pointer">
											Published
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</ScrollArea>
				</div>
			</div>
		</div>
	);
}
