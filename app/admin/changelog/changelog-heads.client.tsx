"use client";

import { buttonVariants } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "nextjs-toploader/app";
import { format } from "date-fns";
import { DashHeader } from "../_components/admin-navigate";
import { ChangelogStatus } from "@/@types/admin/changelog/changelog";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default function ChangelogHeadsComponent({ changelogHeads }: { changelogHeads: any[] }) {
	const router = useRouter();

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader back={[{ href: "/admin", title: "Admin" }]} current={{ title: "Changelog" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 pt-4 text-start md:container">
					<div className="flex w-full flex-row justify-end">
						<NoPrefetchLink href="/admin/changelog/new" className={cn(buttonVariants({ variant: "outline" }), "cursor-pointer")}>
							<Plus className="mr-1 h-4 w-4" />
							New
						</NoPrefetchLink>
					</div>

					<Table>
						<TableHeader className="[&_tr]:border-b-0">
							<TableRow className="bg-muted">
								<TableHead className="font-normal"></TableHead>
								<TableHead className="font-normal">Name</TableHead>
								<TableHead className="font-normal">Version</TableHead>
								<TableHead className="font-normal">Language</TableHead>
								<TableHead className="font-normal">Published Date</TableHead>
								<TableHead className="font-normal">Status</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{changelogHeads.map((changelogHead, index) => (
								<TableRow
									key={index}
									className="cursor-pointer"
									onClick={() => {
										router.push(`/admin/changelog/${changelogHead.id}?_=${new Date().getTime()}`);
									}}
								>
									<TableCell className="text-muted-foreground w-8 py-4">
										<span className="text-sm">{index + 1}</span>
									</TableCell>
									<TableCell className="font-medium text-gray-800">
										<span className="text-sm">{changelogHead.title}</span>
									</TableCell>
									<TableCell className="mx-auto">
										<Badge variant="secondary" className="rounded-full font-normal">
											v{changelogHead.majorVersion}.{changelogHead.minorVersion}.{changelogHead.patchVersion}
										</Badge>
									</TableCell>
									<TableCell className="font-medium text-gray-800">
										<span className="text-sm">{changelogHead.lang}</span>
									</TableCell>
									<TableCell className="text-gray-800">
										<span className="text-sm">{changelogHead.publishedAt ? format(changelogHead.publishedAt, "MMM d, yyyy") : ""}</span>
									</TableCell>
									<TableCell className="mx-auto">
										{changelogHead.status === ChangelogStatus.Published && (
											<Badge variant="secondary" className="rounded-full bg-green-50 font-normal text-green-600 hover:bg-green-50">
												Published
											</Badge>
										)}
										{changelogHead.status === ChangelogStatus.Draft && (
											<Badge variant="secondary" className="rounded-full font-normal">
												Draft
											</Badge>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>
		</div>
	);
}
