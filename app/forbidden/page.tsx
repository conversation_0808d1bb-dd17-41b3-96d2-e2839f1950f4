import { Metadata } from "next";

// export const dynamic = "force-static";
export const metadata: Metadata = {
	robots: {
		index: false,
		follow: false,
	},
};

export default function Page() {
	return (
		<div className="flex min-h-screen flex-col">
			<div className="flex grow items-center justify-center bg-gray-50">
				<div className="mx-auto max-w-lg rounded-lg bg-white p-6 text-center shadow-md">
					<div className="mb-3">
						<svg className="mx-auto h-10 w-10 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
							/>
						</svg>
					</div>
					<h1 className="mb-2 text-xl font-semibold text-gray-900">Access Restricted</h1>
					<p className="mb-4 text-sm text-gray-600">Sorry, we do not offer our services in your region at this time.</p>
				</div>
			</div>
		</div>
	);
}
