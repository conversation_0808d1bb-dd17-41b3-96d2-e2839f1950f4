import { NextResponse } from "next/server";
import { getSessionUserId } from "@/server/auth/auth-session";
import { WEBNAME } from "@/lib/constants";
import { Polar } from "@polar-sh/sdk";
import { handleApiError } from "@/@types/error-api";

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	try {
		const userId = await getSessionUserId();

		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});
		const result = await polar.customerSessions.create({
			customerExternalId: userId,
		});

		return NextResponse.json({ status: 200, url: result.customerPortalUrl });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/payment/portal`);
	}
}
