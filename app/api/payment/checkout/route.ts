import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { WEB_URL, WEBNAME } from "@/lib/constants";
// import { MembershipID } from "@/@types/membership-type";
// import { getUser } from "@/server/utils-user.server";
import { Polar } from "@polar-sh/sdk";
import { handleApiError } from "@/@types/error-api";
import { AuthError } from "@/@types/error";

interface Params {
	productId?: string;
	type: "subscription" | "onetime";
}
export async function POST(req: Request) {
	// const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const params: Params = await req.json();
	if (!params.productId) {
		return NextResponse.json({ status: 400, message: "No variant ID was provided." });
	}

	try {
		// 获取用户信息
		const sessionUser = await getCurrentSessionUser();
		if (!sessionUser) throw new AuthError("Not authorized.");

		let user: any = sessionUser;
		// if (params.type === "subscription") {
		// 	const currentUser = await getUser(sessionUser.id);
		// 	if (currentUser && currentUser.membershipId !== MembershipID.Free) {
		// 		return NextResponse.json({ status: 1001, message: "You are already a member." });
		// 	}
		// }

		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});
		const result = await polar.checkouts.create({
			allowDiscountCodes: true,
			products: [params.productId],
			customerExternalId: user.id,
			customerName: user.name,
			customerEmail: user.email,
			successUrl: `${WEB_URL}/confirmation`,
		});
		return NextResponse.json({ status: 200, url: result.url });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/payment/checkout`);
	}
}

// export const GET = Checkout({
// 	accessToken: process.env.POLAR_ACCESS_TOKEN!,
// 	successUrl: "/confirmation",
// 	server: process.env.NODE_ENV === "production" ? "production" : "sandbox", // Use this option if you're using the sandbox environment - else use 'production' or omit the parameter
// });
