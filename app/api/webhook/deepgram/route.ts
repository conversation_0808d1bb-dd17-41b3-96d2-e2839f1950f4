import { ProjectStatus } from "@/@types/project/project-type";
import { WEBNAME } from "@/lib/constants";
import { extractSpeakers } from "@/lib/utils-project";
import { getDB } from "@/server/db/db-client.server";
import { projectSchema } from "@/server/db/schema.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { generateParagraphsFileUrlPath, saveParagraphsToR2 } from "@/server/utils-project-paragraphs.server";
import { deleteProjectHeadFromKV } from "@/server/utils-project.server";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

// export const config = {
// 	api: {
// 		bodyParser: {
// 			sizeLimit: "1mb",
// 		},
// 	},
// };

export async function POST(request: Request) {
	// 1. check token
	const deepgramToken = request.headers.get("dg-token");
	if (deepgramToken !== process.env.DEEPGRAM_WEBHOOK_KEY) {
		return NextResponse.json("Unauthorized", { status: 401 });
	}
	// console.log("deepgramToken:", deepgramToken);

	// 2. get project uid from transcribe task
	const body: any = await request.json();
	if (process.env.NODE_ENV === "development") {
		console.log("body:", JSON.stringify(body));
		// console.log("body.metadata:", body.metadata);
	}
	const requestId = body.metadata.request_id;
	const db = getDB();
	const [project]: any[] = await db
		.select({
			projectUid: projectSchema.uid,
			speaker: projectSchema.speaker,
		})
		.from(projectSchema)
		.where(eq(projectSchema.requestId, requestId));
	if (!project) {
		return NextResponse.json("The project not found.", { status: 404 });
	}
	const projectUid = project.projectUid;
	const isSpeaker = project.speaker;

	const currentDate = new Date();

	try {
		if (!body.results) {
			throw new Error("body.results is null");
		}
		// get project head
		// const projectHead = await getProjectHeadRealtime(projectUid);
		// console.log("projectHead:", JSON.stringify(projectHead));
		// if (!projectHead) {
		// 	return NextResponse.json("The project not found.", { status: 404 });
		// }

		const paragraphs = body.results?.channels[0]?.alternatives[0]?.paragraphs.paragraphs;
		// 3. if paragraphs array is empty
		if (!paragraphs || paragraphs.length === 0) {
			await db
				.update(projectSchema)
				.set({
					status: ProjectStatus.TranscriptionCompleted,
					isTranscriptEmpty: true,
					updatedAt: currentDate,
				})
				.where(eq(projectSchema.uid, projectUid));
			// delete project head cache in kv
			await deleteProjectHeadFromKV(projectUid);
			return NextResponse.json("OK", { status: 200 });
		}

		// 4 if paragraphs array is not empty, save to r2 and update project head
		// 4.1 将paragraphs作为json文件存储到r2中并得到url path
		let fileParagraphsUrlPath = generateParagraphsFileUrlPath();
		await saveParagraphsToR2(fileParagraphsUrlPath, paragraphs);
		// 4.2 extract speakers
		const updateProjectData: any = {
			status: ProjectStatus.TranscriptionCompleted,
			paragraphsUrlPath: fileParagraphsUrlPath,
			isTranscriptEmpty: false,
			updatedAt: currentDate,
		};
		if (isSpeaker === 0) {
			const speakers = extractSpeakers(paragraphs);
			// console.log("speakers:", speakers);
			updateProjectData.speakerInfo = JSON.stringify(speakers);
		}
		// 4.3 最后将项目基础数据更新存储到数据库中
		await db.update(projectSchema).set(updateProjectData).where(eq(projectSchema.uid, projectUid));

		// delete project head cache in kv
		await deleteProjectHeadFromKV(projectUid);
		return NextResponse.json("OK", { status: 200 });
	} catch (error: any) {
		await db
			.update(projectSchema)
			.set({
				status: ProjectStatus.TranscriptionFailed,
				errorInfo: error.message,
				updatedAt: currentDate,
			})
			.where(eq(projectSchema.uid, projectUid));
		// delete project head cache in kv
		await deleteProjectHeadFromKV(projectUid);

		notifyDevEvent(`${WEBNAME} - api`, `Error - /api/webhook/deepgram`, error.message, null);
		return NextResponse.json(error.message, { status: 500 });
	}
}
