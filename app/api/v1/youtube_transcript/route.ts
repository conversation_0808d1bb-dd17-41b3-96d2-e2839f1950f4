import { NextResponse } from "next/server";
// import { fetchTranscript } from "youtube-transcript-plus";

// https://github.com/ericmmartin/youtube-transcript-plus
// import { YoutubeTranscript } from "youtube-transcript-plus";

export async function POST(req: Request) {
	try {
		// console.log(transcript);
		// return NextResponse.json({ transcript: transcript });

		const params: any = await req.json();
		console.log("params:", params);

		const videoUrl = params.videoUrl;

		if (!videoUrl) {
			return NextResponse.json({ error: "Video URL is required" }, { status: 400 });
		}

		const videoId = extractVideoId(videoUrl);

		if (!videoId) {
			return NextResponse.json({ error: "Invalid YouTube URL" }, { status: 400 });
		}

		// const transcript = await YoutubeTranscript.fetchTranscript(videoId);
		// const text = transcript.map((entry: any) => entry.text).join(" ");

		// return NextResponse.json({ transcript: text });
	} catch (error: any) {
		console.error("Error generating transcript:", error);
		return NextResponse.json({ error: "Failed to generate transcript" }, { status: 500 });
	}
}

// Simple URL parsing (improve as needed)
function extractVideoId(url: string): string | null {
	const urlObj = new URL(url);
	const params = new URLSearchParams(urlObj.search);
	return params.get("v");
}
