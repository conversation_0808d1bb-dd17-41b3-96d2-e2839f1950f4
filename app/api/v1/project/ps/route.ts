import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { projectSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { eq, desc, count } from "drizzle-orm";
import { ProjectHead, ProjectStatus } from "@/@types/project/project-type";

/**
 * 获取项目列表，步骤：
 * 1. 获取用户信息
 * 2. 获取项目信息
 */
export async function POST(req: Request) {
	const url = new URL(req.url);
	const page = parseInt(url.searchParams.get("page") || "1", 10);
	const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
	const startIndex = (page - 1) * pageSize;
	const limit = pageSize;

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const db = getDB();
	const projects: ProjectHead[] = await db
		.select({
			uid: projectSchema.uid,
			fileName: projectSchema.fileName,
			duration: projectSchema.duration,
			language: projectSchema.language,
			status: projectSchema.status,
			share: projectSchema.share,
			createdAt: projectSchema.createdAt,
			isTranscriptEmpty: projectSchema.isTranscriptEmpty,
			fileUrlTemp: projectSchema.fileUrlTemp,
			fileUrlLocal: projectSchema.fileUrlLocal,
		})
		.from(projectSchema)
		.where(eq(projectSchema.userId, sessionUser.id))
		.limit(limit)
		.offset(startIndex)
		.orderBy(desc(projectSchema.id));

	const totalCountData = await db
		.select({
			count: count(projectSchema.id),
		})
		.from(projectSchema)
		.where(eq(projectSchema.userId, sessionUser.id));
	const totalPage = Math.ceil(totalCountData[0].count / pageSize);

	return NextResponse.json({ status: 200, message: "success", projects: projects, totalPage });
}
