import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { projectSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { eq, and } from "drizzle-orm";
import { z } from "zod";

const paramsSchema = z.object({
	pid: z.string().nonempty(),
});
type ParamsType = z.infer<typeof paramsSchema>;

/**
 * 获取项目详情，步骤：
 * 1. 获取用户信息
 * 2. 获取项目信息
 */
export async function POST(req: Request) {
	const params: ParamsType = await req.json();
	try {
		paramsSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const db = getDB();
	const projects = await db
		.select({
			uid: projectSchema.uid,
			status: projectSchema.status,
			fileName: projectSchema.fileName,
			fileUrlTemp: projectSchema.fileUrlTemp,
			fileUrlLocal: projectSchema.fileUrlLocal,
			duration: projectSchema.duration,
			language: projectSchema.language,
			speakerInfo: projectSchema.speakerInfo,
			paragraphsUrlPath: projectSchema.paragraphsUrlPath,
			isTranscriptEmpty: projectSchema.isTranscriptEmpty,
			createdAt: projectSchema.createdAt,
			updatedAt: projectSchema.updatedAt,
		})
		.from(projectSchema)
		.where(and(eq(projectSchema.uid, params.pid), eq(projectSchema.userId, sessionUser.id)));

	if (projects.length === 0) {
		return NextResponse.json({ status: 404, message: "Project not found." });
	}
	const project = projects[0];
	if (project.speakerInfo) {
		project.speakerInfo = JSON.parse(project.speakerInfo);
	}
	// console.log("project:", project);

	return NextResponse.json({ status: 200, message: "success", project });
}
