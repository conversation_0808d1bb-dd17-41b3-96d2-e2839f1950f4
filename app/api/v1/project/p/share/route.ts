import { and, eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { getDB } from "@/server/db/db-client.server";
import { z } from "zod";
import { projectSchema } from "@/server/db/schema.server";
import { deleteProjectHeadFromKV } from "@/server/utils-project.server";

const paramsSchema = z.object({
	pid: z.string().nonempty(),
	share: z.number(),
});
type ParamsType = z.infer<typeof paramsSchema>;

export async function POST(req: Request) {
	const params: ParamsType = await req.json();
	try {
		paramsSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	//  Get user
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const db = getDB();
	await db
		.update(projectSchema)
		.set({
			share: params.share,
			updatedAt: new Date(),
		})
		.where(and(eq(projectSchema.uid, params.pid), eq(projectSchema.userId, sessionUser.id)));

	// Delete project head from kv
	await deleteProjectHeadFromKV(params.pid);

	return NextResponse.json({ status: 200, message: "ok", share: params.share });
}
