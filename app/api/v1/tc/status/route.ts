import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { getProjectHead, ProjectHeadServer } from "@/server/utils-project.server";

interface Params {
	pid: string;
}

/**
 * 获取转录任务状态
 */
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.pid) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	//test>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
	// const currentDate = new Date();
	// console.log(`---------------------Start translate index(${params.i}) at ${currentDate.toLocaleString()}`);
	// const startTime = currentDate.getTime();
	//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	// const projectHead = await getProjectHead(params.pid);
	// if (!projectHead) {
	// 	return NextResponse.json({ status: 404, message: "Transcription task not found." });
	// }

	const projectHead: ProjectHeadServer | null = await getProjectHead(params.pid);
	if (!projectHead) {
		return NextResponse.json({ status: 404, message: "Transcription task not found." });
	}
	return NextResponse.json({ status: 200, task_status: projectHead.status });
}
