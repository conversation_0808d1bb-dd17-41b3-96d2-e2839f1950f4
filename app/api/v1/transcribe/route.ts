import { NextResponse } from "next/server";
import { WEBNAME } from "@/lib/constants";
import { getSessionUserId } from "@/server/auth/auth-session";
import { z } from "zod";
import { MembershipID } from "@/@types/membership-type";
import { getUser } from "@/server/utils-user.server";
import { handleApiError } from "@/@types/error-api";
import { transcribeFromDeepinfra } from "@/server/ai/deepinfra.server";
import { checkFreeUserTranscribeLimit } from "@/server/kv/redis-upstash.server";
import { FreeLimitError } from "@/@types/error";
import { getDB } from "@/server/db/db-client.server";
import { projectSchema, transcribeTaskSchema } from "@/server/db/schema.server";
import { getUUIDString } from "@/lib/utils";
import { TranscribePlatform } from "@/lib/utils-transcribe";
import { ProjectStatus } from "@/@types/project/project-type";
import { checkUserCredit, updateUserCredit, UserCreditConsume } from "@/server/utils-credits.server";
import { transcribeFromFireworks } from "@/server/ai/fireworks.server";

const paramsSchema = z.object({
	source: z.string(),
	fileUrl: z.string().url(),
	fileName: z.string().optional(),
	lang: z.string(),
	speaker: z.boolean(),
	d: z.number().optional(),
});
type Params = z.infer<typeof paramsSchema>;

/**
 * 创建转录任务，步骤:
 * 1. 检查参数
 * 2. 检查用户token
 * 3. 创建转录任务并保存到数据库
 * 4. 更新用户credits
 */
export async function POST(req: Request) {
	const cfIp = req.headers.get("cf-connecting-ip");

	// 1. 检查参数
	const params: Params = await req.json();
	try {
		if (process.env.NODE_ENV === "development") {
			console.log("params:", params);
		}
		paramsSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	try {
		const userId = await getSessionUserId();
		// const user = await getUserRealtime(userId);
		const user = await getUser(userId);
		if (!user) {
			return NextResponse.json({ status: 401, message: "Not authorized." });
		}
		// 2. check credits
		if (user.membershipId === MembershipID.Free) {
			// limit 3 times transcribe for free user
			if (!(await checkFreeUserTranscribeLimit(userId))) {
				throw new FreeLimitError("You have reached the transcribe daily limit. Upgrade to a paid plan to transcribe more.");
			}
		}
		let creditConsumes: UserCreditConsume[] | null = null;
		if (user.membershipId === MembershipID.Starter) {
			const creditsInfo = await checkUserCredit(userId, {
				needCredits: Math.ceil(params.d! / 60),
				existUser: user,
			});
			creditConsumes = creditsInfo.creditConsumes;
			if (process.env.NODE_ENV === "development") {
				console.log("creditConsumes:", creditConsumes);
			}
		}

		let transcribePlatform;
		let sentences: SentenceData[] = [];
		if (params.speaker) {
			transcribePlatform = TranscribePlatform.Fireworks;
			sentences = await transcribeFromFireworks(params.fileUrl, params.lang);
		} else {
			transcribePlatform = TranscribePlatform.Deepinfra;
			sentences = await transcribeFromDeepinfra(params.fileUrl, params.lang);
		}
		if (process.env.NODE_ENV === "development") {
			console.log("sentences:", sentences);
		}

		let remark;
		if (user.membershipId !== MembershipID.Free) {
			remark = "Free";
		}
		if (user.membershipId !== MembershipID.Pro) {
			remark = "Pro";
		}

		const projectUid = getUUIDString();
		const db = getDB();
		await db.insert(transcribeTaskSchema).values({
			userId: userId,
			platform: transcribePlatform,
			requestId: projectUid,
			status: ProjectStatus.TranscriptionCompleted,
			fileName: params.fileName!,
			fileUrlTemp: params.fileUrl,
			duration: params.d ?? 0,
			language: params.lang,
			speaker: params.speaker ? 0 : null,
			creditsSources: creditConsumes ? JSON.stringify(creditConsumes) : null,
			ip: cfIp,
			remark: remark,
		});
		if (user.membershipId === MembershipID.Starter) {
			await updateUserCredit(userId, creditConsumes!, {
				remark: `Transcribe task id: ${projectUid}.`,
			});
		}

		return NextResponse.json({ message: "Success", sentences, diarize: params.speaker });

		// // 2. 通过文件url获取元数据（文件名、时长、size）
		// let fileName = "";
		// let fileDuration = 0;
		// // let fileSize = 0;
		// if (params.source === "local") {
		// 	fileName = params.fileName ?? "";
		// 	fileDuration = params.d ?? 0;
		// } else {
		// 	try {
		// 		const { media } = await ofetch("https://fal.run/fal-ai/ffmpeg-api/metadata", {
		// 			method: "POST",
		// 			headers: {
		// 				"Content-Type": "application/json",
		// 				Authorization: `Key ${process.env.FAL_KEY}`,
		// 			},
		// 			body: {
		// 				media_url: params.fileUrl,
		// 			},
		// 		});
		// 		// console.log("media:", media);
		// 		const mediaType = media.media_type as string;
		// 		if (!mediaType.includes("video") && !mediaType.includes("audio")) {
		// 			return NextResponse.json({ status: 501, message: "Unsupported media type." });
		// 		}
		// 		fileName = media.file_name;
		// 		fileDuration = media.duration;
		// 		// fileSize = media.file_size;
		// 	} catch (error) {
		// 		console.error("Error fetching media metadata:", error);
		// 		return NextResponse.json({ status: 501, message: "Failed to fetch media metadata." });
		// 	}
		// }

		// // 3 查询用户最新数据（需要用到credits）
		// // 3.1 check file duration free/stater
		// if (user.membershipId === MembershipID.Free) {
		// 	// 免费用户最大文件时长为30分钟
		// 	if (fileDuration > FILE_DURATION_LIMIT_FREE) {
		// 		return NextResponse.json({ status: 5001, message: "Free users can only upload files up to 60 minutes." });
		// 	}
		// }
		// if (user.membershipId === MembershipID.Starter) {
		// 	// 付费用户最大文件时长为2小时
		// 	if (fileDuration > FILE_DURATION_LIMIT_STATER) {
		// 		return NextResponse.json({ status: 5001, message: "Stater users can only upload files up to 2 hours." });
		// 	}
		// }

		// // 4. 创建转录任务 (include 3.2 check credits)
		// const { status, message, newProjectHead } = await CreateTranscribeTask(
		// 	{
		// 		source: params.source,
		// 		lang: params.lang,
		// 		speaker: params.speaker,
		// 		fileName: fileName,
		// 		fileUrl: params.fileUrl,
		// 		fileDuration: fileDuration,
		// 	},
		// 	user,
		// 	cfIpCountryCode,
		// );
		// if (status !== 200) return NextResponse.json({ status, message });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/transcribe`);
	}
}
