import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { createR2Url } from "@/server/r2.server";
import { z } from "zod";
import { getProjectHead, ProjectHeadServer } from "@/server/utils-project.server";

const paramsSchema = z.object({
	pid: z.string().nonempty(),
	language: z.string().nonempty(),
});
type ParamsType = z.infer<typeof paramsSchema>;

export async function POST(req: Request) {
	const params: ParamsType = await req.json();
	try {
		paramsSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 1. 从数据库获取用户信息
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	//2. 从数据库查询project subtitle head基础数据
	const projectHead: ProjectHeadServer | null = await getProjectHead(params.pid, sessionUser.id);
	if (!projectHead || !projectHead.paragraphsUrlPath) {
		return NextResponse.json({ status: 500, message: "project's transcript not found." });
	}

	try {
		const path = projectHead.paragraphsUrlPath;
		const signedUrl = await createR2Url(path, "application/json");
		return NextResponse.json({ status: 200, url: signedUrl, method: "PUT" });
	} catch (error: any) {
		return NextResponse.json({ status: 500, message: error.message || "Failed to update transcript." });
	}
}
