type LanguageType = {
	value: string;
	name: string;
	splitSpace: string;
};

export const getLanguages: LanguageType[] = [
	{ value: "en", name: "English", splitSpace: " " },
	{ value: "zh", name: "Chinese", splitSpace: "" },
	{ value: "de", name: "German", splitSpace: " " },
	{ value: "es", name: "Spanish", splitSpace: " " },
	{ value: "ru", name: "Russian", splitSpace: " " },
	{ value: "ko", name: "Korean", splitSpace: "" },
	{ value: "fr", name: "French", splitSpace: " " },
	{ value: "ja", name: "Japanese", splitSpace: "" },
	{ value: "pt", name: "Portuguese", splitSpace: " " },
	{ value: "tr", name: "Turkish", splitSpace: " " },
	{ value: "pl", name: "Polish", splitSpace: " " },
	{ value: "ca", name: "Catalan", splitSpace: " " },
	{ value: "nl", name: "Dutch", splitSpace: " " },
	{ value: "ar", name: "Arabic", splitSpace: " " },
	{ value: "sv", name: "Swedish", splitSpace: " " },
	{ value: "it", name: "Italian", splitSpace: " " },
	{ value: "id", name: "Indonesian", splitSpace: " " },
	{ value: "hi", name: "Hindi", splitSpace: " " },
	{ value: "fi", name: "Finnish", splitSpace: " " },
	{ value: "vi", name: "Vietnamese", splitSpace: " " },
	{ value: "he", name: "Hebrew", splitSpace: " " },
	{ value: "uk", name: "Ukrainian", splitSpace: " " },
	{ value: "el", name: "Greek", splitSpace: " " },
	{ value: "ms", name: "Malay", splitSpace: " " },
	{ value: "cs", name: "Czech", splitSpace: " " },
	{ value: "ro", name: "Romanian", splitSpace: " " },
	{ value: "da", name: "Danish", splitSpace: " " },
	{ value: "hu", name: "Hungarian", splitSpace: " " },
	{ value: "ta", name: "Tamil", splitSpace: " " },
	{ value: "no", name: "Norwegian", splitSpace: " " },
	{ value: "th", name: "Thai", splitSpace: "" },
	{ value: "ur", name: "Urdu", splitSpace: " " },
	{ value: "hr", name: "Croatian", splitSpace: " " },
	{ value: "bg", name: "Bulgarian", splitSpace: " " },
	{ value: "lt", name: "Lithuanian", splitSpace: " " },
	{ value: "la", name: "Latin", splitSpace: " " },
	{ value: "mi", name: "Maori", splitSpace: " " },
	{ value: "ml", name: "Malayalam", splitSpace: " " },
	{ value: "cy", name: "Welsh", splitSpace: " " },
	{ value: "sk", name: "Slovak", splitSpace: " " },
	{ value: "te", name: "Telugu", splitSpace: " " },
	{ value: "fa", name: "Persian", splitSpace: " " },
	{ value: "lv", name: "Latvian", splitSpace: " " },
	{ value: "bn", name: "Bengali", splitSpace: " " },
	{ value: "sr", name: "Serbian", splitSpace: " " },
	{ value: "az", name: "Azerbaijani", splitSpace: " " },
	{ value: "sl", name: "Slovenian", splitSpace: " " },
	{ value: "kn", name: "Kannada", splitSpace: " " },
	{ value: "et", name: "Estonian", splitSpace: " " },
	{ value: "mk", name: "Macedonian", splitSpace: " " },
	{ value: "br", name: "Breton", splitSpace: " " },
	{ value: "eu", name: "Basque", splitSpace: " " },
	{ value: "is", name: "Icelandic", splitSpace: " " },
	{ value: "hy", name: "Armenian", splitSpace: " " },
	{ value: "ne", name: "Nepali", splitSpace: " " },
	{ value: "mn", name: "Mongolian", splitSpace: " " },
	{ value: "bs", name: "Bosnian", splitSpace: " " },
	{ value: "kk", name: "Kazakh", splitSpace: " " },
	{ value: "sq", name: "Albanian", splitSpace: " " },
	{ value: "sw", name: "Swahili", splitSpace: " " },
	{ value: "gl", name: "Galician", splitSpace: " " },
	{ value: "mr", name: "Marathi", splitSpace: " " },
	{ value: "pa", name: "Punjabi", splitSpace: " " },
	{ value: "si", name: "Sinhala", splitSpace: " " },
	{ value: "km", name: "Khmer", splitSpace: "" },
	{ value: "sn", name: "Shona", splitSpace: " " },
	{ value: "yo", name: "Yoruba", splitSpace: " " },
	{ value: "so", name: "Somali", splitSpace: " " },
	{ value: "af", name: "Afrikaans", splitSpace: " " },
	{ value: "oc", name: "Occitan", splitSpace: " " },
	{ value: "ka", name: "Georgian", splitSpace: " " },
	{ value: "be", name: "Belarusian", splitSpace: " " },
	{ value: "tg", name: "Tajik", splitSpace: " " },
	{ value: "sd", name: "Sindhi", splitSpace: " " },
	{ value: "gu", name: "Gujarati", splitSpace: " " },
	{ value: "am", name: "Amharic", splitSpace: " " },
	{ value: "yi", name: "Yiddish", splitSpace: " " },
	{ value: "lo", name: "Lao", splitSpace: "" },
	{ value: "uz", name: "Uzbek", splitSpace: " " },
	{ value: "fo", name: "Faroese", splitSpace: " " },
	{ value: "ht", name: "Haitian Creole", splitSpace: " " },
	{ value: "ps", name: "Pashto", splitSpace: " " },
	{ value: "tk", name: "Turkmen", splitSpace: " " },
	{ value: "nn", name: "Nynorsk", splitSpace: " " },
	{ value: "mt", name: "Maltese", splitSpace: " " },
	{ value: "sa", name: "Sanskrit", splitSpace: " " },
	{ value: "lb", name: "Luxembourgish", splitSpace: " " },
	{ value: "my", name: "Myanmar", splitSpace: "" },
	{ value: "bo", name: "Tibetan", splitSpace: "" },
	{ value: "tl", name: "Tagalog", splitSpace: " " },
	{ value: "mg", name: "Malagasy", splitSpace: " " },
	{ value: "as", name: "Assamese", splitSpace: " " },
	{ value: "tt", name: "Tatar", splitSpace: " " },
	{ value: "haw", name: "Hawaiian", splitSpace: " " },
	{ value: "ln", name: "Lingala", splitSpace: " " },
	{ value: "ha", name: "Hausa", splitSpace: " " },
	{ value: "ba", name: "Bashkir", splitSpace: " " },
	{ value: "jw", name: "Javanese", splitSpace: " " },
	{ value: "su", name: "Sundanese", splitSpace: " " },
	{ value: "yue", name: "Cantonese", splitSpace: "" },
];

export const getLanguageName = (value: string): string | undefined => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.name;
};

export const getLanguageSplitSpace = (value: string): string => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.splitSpace ?? " ";
};
export const getLanguageSplitSpaceLength = (value: string): number => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.splitSpace.length ?? 0;
};
