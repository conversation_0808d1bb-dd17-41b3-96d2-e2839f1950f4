import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import * as uuid from "uuid";
import { nanoid } from "nanoid";
import { isNil } from "lodash";
import { i18nConfig } from "@/i18n-config";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// >>>>>>>>>>>>>>>>>>>>>Generate ID<<<<<<<<<<<<<<<<<<<<<<<<<<<<
export function getUUIDString(): string {
	return uuid.v7().replace(/-/g, "");
}
export function getNanoId(length?: number): string {
	if (length) return nanoid(length);
	return nanoid();
}

// KV key
export const KV_KEY_USER = "user";
export const getKVKeyUser = (userUid: string) => {
	return `${KV_KEY_USER}:${userUid}`;
};
export const KV_KEY_FREE_TRANSCRIBE_LIMIT = "free-limit";
export const getKVKeyFreeTranscribeLimit = (userUid: string) => {
	return `${KV_KEY_FREE_TRANSCRIBE_LIMIT}:${userUid}`;
};
// KV key for project
const KV_KEY_PROJECT_HEAD = "project-head";
export const getKVKeyProjectHead = (projectUid: string) => {
	return `${KV_KEY_PROJECT_HEAD}:${projectUid}`;
};
// KV key for blog
export const getKVKeyBlogHeads = (page: number) => {
	return `blogs:${page}`;
};
export const getKVKeyBlog = (lang: string | undefined | null, slug: string) => {
	if (lang && lang !== i18nConfig.defaultLocale) {
		return `blog:${lang}:${slug}`;
	}
	return `blog:${slug}`;
};
export const KV_KEY_BLOG_CATEGORIES = "blog_categories";
// KV key for changelog
export const getKVKeyChangelogHeads = (page: number) => {
	return `changelogs:${page}`;
};

// Download file
export const handleDownloadFileByUrl = async (url?: string) => {
	if (!url) return;
	const response = await fetch(url);
	const blob = await response.blob();
	const link = document.createElement("a");
	link.href = window.URL.createObjectURL(blob);
	link.download = getNanoId(10) + ".wav";
	link.click();
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
