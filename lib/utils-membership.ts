import { MembershipID, membershipMapping, MembershipPeriodMonth, MembershipPeriodType, MembershipPeriodYear, MembershipType } from "@/@types/membership-type";

// ======================order======================
// product dev
const ORDER_DEV_PRODUCT_ID_1 = "980b2fb5-0bf3-4d5f-825d-0648fa04952b";
const ORDER_DEV_PRODUCT_ID_2 = "d6d01824-9f49-423b-9bfb-2602f159290b";
const ORDER_DEV_PRODUCT_ID_3 = "321c6ead-7789-4ee2-9bc2-e34158a03c20";
// product prod
const ORDER_PROD_PRODUCT_ID_1 = "f75f20e7-68ab-4859-87bb-0b18b2b53e0f";
const ORDER_PROD_PRODUCT_ID_2 = "32ee801e-e2f0-45ab-810c-93929847bda1";
const ORDER_PROD_PRODUCT_ID_3 = "f7c8a2f1-c2df-46b7-b682-24b821aaa380";

export const ORDER_PRODUCT_ID_1 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_1 : ORDER_DEV_PRODUCT_ID_1;
export const ORDER_PRODUCT_ID_2 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_2 : ORDER_DEV_PRODUCT_ID_2;
export const ORDER_PRODUCT_ID_3 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_3 : ORDER_DEV_PRODUCT_ID_3;

export interface OrderInfo {
	productId: string;
	membership: MembershipType;
	credits: number;
}

const orderVariants: OrderInfo[] = [
	{
		productId: ORDER_PRODUCT_ID_1,
		membership: membershipMapping[MembershipID.Pro],
		credits: 10,
	},
	{
		productId: ORDER_PRODUCT_ID_2,
		membership: membershipMapping[MembershipID.Pro],
		credits: 50,
	},

	{
		productId: ORDER_PRODUCT_ID_3,
		membership: membershipMapping[MembershipID.Pro],
		credits: 80,
	},
];
export function getOrderProductInfo(productId: string | number): OrderInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return orderVariants.find((v) => v.productId === productId);
}

export enum OrderSource {
	Polar = "polar",
	Cream = "cream",
	Lmsqueezy = "lemonsqueezy",
}

// pending, paid, refunded, partially_refunded
export enum OrderStatus {
	Pending = "pending",
	Paid = "paid",
	Refunded = "refunded",
	PartiallyRefunded = "partially_refunded",
}
export function getOrderStatusName(status: OrderStatus): string {
	switch (status) {
		case OrderStatus.Pending:
			return "Pending";
		case OrderStatus.Paid:
			return "Paid";
		case OrderStatus.Refunded:
			return "Refunded";
		case OrderStatus.PartiallyRefunded:
			return "Partially Refunded";
		default:
			return "Unknown";
	}
}

// ======================Subscription Polar=====================
// incomplete, incomplete_expired, trialing, active, past_due, canceled, unpaid
export enum SubscriptionStatus {
	Incomplete = "incomplete",
	IncompleteExpired = "incomplete_expired",
	Trialing = "trialing",
	Active = "active",
	PastDue = "past_due",
	Canceled = "canceled",
	Unpaid = "unpaid",
}
export function getSubscriptionStatusName(status: SubscriptionStatus): string {
	switch (status) {
		case SubscriptionStatus.Incomplete:
			return "Incomplete";
		case SubscriptionStatus.IncompleteExpired:
			return "Incomplete Expired";
		case SubscriptionStatus.Trialing:
			return "Trialing";
		case SubscriptionStatus.Active:
			return "Active";
		case SubscriptionStatus.PastDue:
			return "Past Due";
		case SubscriptionStatus.Canceled:
			return "Canceled";
		case SubscriptionStatus.Unpaid:
			return "Unpaid";
		default:
			return "Unknown";
	}
}
// product dev
const SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_MONTH = "1fef09cd-4989-47af-88f4-0892423eec5c";
const SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_YEAR = "5cbf1d03-3d2b-4bb4-93f1-e39c1f381902";
const SUBSCRIPTION_DEV_PRODUCT_ID_PRO_MONTH = "5c510a3d-1367-4f40-a428-d7da3a3fde34";
const SUBSCRIPTION_DEV_PRODUCT_ID_PRO_YEAR = "49fbba63-fd51-49bf-9d3f-2f082473a140";
// product prod
const SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_MONTH = "028556d1-9e7b-47a2-9669-4884d23a77f4";
const SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_YEAR = "9c7c5ce7-1fdb-4729-b5ac-982867dff241";
const SUBSCRIPTION_PROD_PRODUCT_ID_PRO_MONTH = "7203a82a-c10b-40d6-9633-6d3c07fe6673";
const SUBSCRIPTION_PROD_PRODUCT_ID_PRO_YEAR = "add82adc-f847-46c2-b4bc-e13417df2865";

export const SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_MONTH : SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_MONTH;
export const SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_YEAR : SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_YEAR;
export const SUBSCRIPTION_PRODUCT_ID_PRO_MONTH =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_PRO_MONTH : SUBSCRIPTION_DEV_PRODUCT_ID_PRO_MONTH;
export const SUBSCRIPTION_PRODUCT_ID_PRO_YEAR =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_PRO_YEAR : SUBSCRIPTION_DEV_PRODUCT_ID_PRO_YEAR;

export interface MembershipInfo {
	productId: string;
	membership: MembershipType;
	period: MembershipPeriodType;
}

const membershipVariants: MembershipInfo[] = [
	{
		productId: SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodYear,
	},

	{
		productId: SUBSCRIPTION_PRODUCT_ID_PRO_MONTH,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_PRO_YEAR,
		membership: membershipMapping[MembershipID.Pro],
		period: MembershipPeriodYear,
	},
];
export function getMembershipProductInfo(productId: string | number): MembershipInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return membershipVariants.find((v) => v.productId === productId);
}

// 限制用户会员降级，但这不是最好的方案，更好的方案为：**只能在下一个收费周期进行一个会员顶级更改**
// Starter月会员能更改升级成：Starter年会员、_PRO_月会员、_PRO_年会员
// Starter年会员能更改能升级成：_PRO_年会员
// _PRO_月会员能更改能升级成：Starter年会员、_PRO_年会员
// _PRO_年会员不能进行任何更改
export function canChangePlan(userProductId: string | number, targetProductId: string | number): boolean {
	const userMembershipInfo = getMembershipProductInfo(userProductId);
	if (!userMembershipInfo) return true;
	const targetMembershipInfo = getMembershipProductInfo(targetProductId);
	if (!targetMembershipInfo) return true;

	//Pro年会员不能进行任何更改
	if (userMembershipInfo.membership.id === MembershipID.Pro && userMembershipInfo.period.value === MembershipPeriodYear.value) {
		return false;
	}
	// Starter月会员能更改升级成：Starter年会员、Pro月会员、Pro年会员
	if (userMembershipInfo.membership.id === MembershipID.Starter && userMembershipInfo.period.value === MembershipPeriodMonth.value) {
		return true;
	}
	// Starter年会员只能更改能升级成：Pro年会员
	if (userMembershipInfo.membership.id === MembershipID.Starter && userMembershipInfo.period.value === MembershipPeriodYear.value) {
		if (targetMembershipInfo.membership.id === MembershipID.Pro && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
		return false;
	}
	// Pro月会员只能更改能升级成：Starter年会员、Pro年会员
	if (userMembershipInfo.membership.id === MembershipID.Pro && userMembershipInfo.period.value === MembershipPeriodMonth.value) {
		if (targetMembershipInfo.membership.id === MembershipID.Pro && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
		if (targetMembershipInfo.membership.id === MembershipID.Starter && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
	}

	return false;
}
