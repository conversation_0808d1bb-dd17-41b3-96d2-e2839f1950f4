import { OSS_URL } from "./constants";

// photo style category
export enum StyleCategoryID {
	None = 0, // prompt only
	Reference = 1, //  reference image + prompt(optional)
	Cartoon = 10,
	ActionFigure = 11,
	Sketch = 12,
	Artist = 13,
	Art = 14,
}
export type StyleCategory = {
	id: number;
	name: string;
};

// photo style model
export enum PhotoStyleID {
	None = 0, // prompt only
	Reference = 1, //  reference image + prompt(optional)
	Cartoon = 1000,
	Ghibli = 1001,
	Lego = 1002,
	Anime = 1003,
	Pixel = 1004,
	Minecraft = 1005,
	Pixar = 1006, // Disney pixar style
	Barbie = 1007,
	SimpsonStyle = 1008,
	<PERSON>bi = 1009,
	SouthPark = 1010, // South park style
	Clay = 1011,
	Muppet = 1012, // Disney muppet style
	RickMorty = 1013, // Rick and morty style
	// RubberHoseAnimation = 1014,
	ActionFigure1 = 1100,
	ActionFigureBarbie = 1101,
	ActionFigureBusiness = 1103,
	ActionFigureSoccerPlayer = 1104,
	ActionFigureFunkoPop = 1005,
	// ActionFigureBasketballPlayer = 1105,
	// ActionFigureDoctor = 1106,
	// ActionFigureTeacher = 1107,
	// ActionFigurePoliceman = 1108,
	// ActionFigureFirefighter = 1109,
	// ActionFigureDesigner = 1105,
	// ActionFigureGardener = 1107,
	// ActionFigureRunner = 1108,
	// ActionFigureChef = 1109,
	// ActionFigureArtist = 1109,
	// ActionFigureEngineer = 1110,
	// ActionFigureSoldier = 1115,
	// ActionFigureScientist = 1116,
	Outline = 1200,
	LineArt = 1201,
}
export type StyleType = {
	id: number;
	name: string;
	prompt?: string;
	previewImageShort?: string;
	previewImageDemo?: string;
	demoImage?: {
		before: string;
		after: string;
	};
	pro?: boolean;
};
export type PhotoStyleType = {
	category: StyleCategory;
	style: StyleType[];
	promptPlaceholder?: string;
};
export const getPhotoStyleTypes: PhotoStyleType[] = [
	{
		promptPlaceholder: "Add specific instructions for the Al (e.g., Make it more colorful)",
		category: {
			id: StyleCategoryID.Cartoon,
			name: "Cartoon",
		},
		style: [
			{
				id: PhotoStyleID.Cartoon,
				name: "Cartoon",
				prompt: "Create a cartoon version of this image",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/cartoon_short.webp`,
				demoImage: {
					before: `${OSS_URL}/mkt/page/cartoon/cartoon-demo-before.webp`,
					after: `${OSS_URL}/mkt/page/cartoon/cartoon-demo-after.webp`,
				},
			},
			{
				pro: true,
				id: PhotoStyleID.Ghibli,
				name: "Ghibli",
				prompt: "restyle image in studio Ghibli style keep all details",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/ghibli_short.webp`,
				previewImageDemo: `${OSS_URL}/mkt/tools/photo_to_ghibli.webp`,
			},
			{
				id: PhotoStyleID.Lego,
				name: "Lego",
				prompt: "restyle image in Lego style, detailed, 3D-like",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/lego_short.webp`,
				previewImageDemo: `${OSS_URL}/mkt/tools/lego-filter.webp`,
			},
			{
				id: PhotoStyleID.Anime,
				name: "Anime",
				prompt: "restyle image in Anime style keep all details",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/anime_short.webp`,
				previewImageDemo: `${OSS_URL}/mkt/tools/photo-to-anime.webp`,
			},
			{
				id: PhotoStyleID.Pixel,
				name: "Pixel",
				prompt: "Transform the image into polished pixel art style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/pixel_short1.webp`,
				demoImage: {
					before: `${OSS_URL}/mkt/page/pixel/feature-example-2-before.webp`,
					after: `${OSS_URL}/mkt/page/pixel/feature-example-2-after.webp`,
				},
			},
			{
				id: PhotoStyleID.Minecraft,
				name: "Minecraft",
				prompt: "Transform the image into minecraft style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/minecraft_short.webp`,
			},
			{
				id: PhotoStyleID.Pixar,
				name: "Pixar",
				prompt: "Transform the image into disney pixar style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/pixar_short.webp`,
			},
			{
				id: PhotoStyleID.Barbie,
				name: "Barbie",
				prompt: "Transform the image into Barbie doll style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/barbie_short.webp`,
			},
			{
				id: PhotoStyleID.SimpsonStyle,
				name: "Simpsons",
				prompt: "Transform the image into simpsons style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/simpsons_short.webp`,
			},
			{
				id: PhotoStyleID.Chibi,
				name: "Chibi",
				prompt: "Transform the image into Chibi style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/chibi_short.webp`,
			},
			{
				id: PhotoStyleID.SouthPark,
				name: "South Park",
				prompt: "Turn this image into a South Park-style flat 2D cutout animation with bold outlines, minimalistic shapes, and a raw, satirical edge.",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/south_park_short.webp`,
			},
			{
				id: PhotoStyleID.Clay,
				name: "Clay",
				prompt: "Transform the image into a claymation",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/clay_short.webp`,
			},
			{
				id: PhotoStyleID.Muppet,
				name: "Muppet",
				prompt: "Transform the image into Muppets style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/muppet_short.webp`,
			},
			{
				id: PhotoStyleID.RickMorty,
				name: "Rick Morty",
				prompt: "Transform the image into Rick and Morty style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/rick_morty_short.webp`,
				demoImage: {
					before: `${OSS_URL}/mkt/page/photo-effects/rick-morty/demo-before.webp`,
					after: `${OSS_URL}/mkt/page/photo-effects/rick-morty/demo-after.webp`,
				},
			},
		],
	},
	{
		promptPlaceholder: `Add specific instructions for the Al (e.g., On top of the box, write "[TITLE]". The accessories include [ACCESSORIES].)`,
		category: {
			id: StyleCategoryID.ActionFigure,
			name: "Action Figure & Toy",
		},
		style: [
			{
				pro: true,
				id: PhotoStyleID.ActionFigure1,
				name: "Action Figure",
				prompt: "Draw a photorealistic action figure toy of the person in this photo. The figure should be full figure and displayed in it original blister pack packaging. In the blister pack packaging, next to the figure show some toy's accessories. This figure should look like a premium toy and everything should be inside the plastic",
				previewImageShort: `${OSS_URL}/mkt/app/style/action-figure/action_figure_short.webp`,
				previewImageDemo: `${OSS_URL}/mkt/tools/action_figure_generator.webp`,
			},
			{
				pro: true,
				id: PhotoStyleID.ActionFigureBarbie,
				name: "Barbie",
				prompt: "Draw a photorealistic action figure toy inspired by a Barbie doll in this photo. The figure should be full figure, capturing Barbie's iconic facial features and a glamorous outfit (e.g., a sparkly pink dress, high heels, and tiara). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show some toy accessories(e.g., a purse, sunglasses, a hairbrush, a compact mirror, or a jewelry box). This figure should look like a premium toy, and everything should be inside the plastic.",
				previewImageShort: `${OSS_URL}/mkt/app/style/action-figure/barbie_short.webp`,
			},
			{
				pro: true,
				id: PhotoStyleID.ActionFigureBusiness,
				name: "Business",
				prompt: "Draw a photorealistic action figure toy of a business professional in this photo. The figure should be full figure, with detailed facial features and a sharp business outfit (e.g., a tailored suit, tie, and dress shoes). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show some toy accessories inspired(e.g., a briefcase, a laptop, a smartphone, a coffee mug, or a notepad). This figure should look like a premium toy, and everything should be inside the plastic.",
				previewImageShort: `${OSS_URL}/mkt/app/style/action-figure/business_short.webp`,
			},
			{
				pro: true,
				id: PhotoStyleID.ActionFigureSoccerPlayer,
				name: "Soccer Player",
				prompt: "Draw a photorealistic action figure toy of a soccer player in this photo. The figure should be full figure, with accurate facial features and a soccer uniform (e.g., a jersey, shorts, cleats, and shin guards). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show some toy accessories(e.g., a soccer ball, a water bottle, a captain’s armband, a mini goal net, or a whistle). This figure should look like a premium toy, and everything should be inside the plastic.",
				previewImageShort: `${OSS_URL}/mkt/app/style/action-figure/soccer_player_short.webp`,
			},
			{
				pro: true,
				id: PhotoStyleID.ActionFigureFunkoPop,
				name: "Funko Pop",
				prompt: "Transform the image into a Funko Pop figure",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/funko_pop_short.webp`,
			},
			// {
			// 	id: PhotoStyleID.ActionFigureBasketballPlayer,
			// 	name: "Basketball Player",
			// 	prompt: "Draw an action figure toy of a basketball player in this photo. The figure should be full figure, with detailed facial features and a basketball uniform (e.g., a jersey, shorts, sneakers, and a headband). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show 3-5 toy accessories inspired by the basketball player's outfit or sport (e.g., a basketball, a water bottle, a gym bag, a mini hoop, or a wristband), ensuring no accessories are body parts. This figure should look like a premium toy, and everything should be inside the plastic.",
			// },
			// {
			// 	id: PhotoStyleID.ActionFigureDoctor,
			// 	name: "Doctor",
			// 	prompt: "Draw an action figure toy of a doctor in this photo. The figure should be full figure, with precise facial features and a medical outfit (e.g., a white lab coat, scrubs, and a stethoscope around the neck). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show 3-5 toy accessories inspired by the doctor's outfit or profession (e.g., a stethoscope, a clipboard, a medical bag, a syringe, or a thermometer), ensuring no accessories are body parts. This figure should look like a premium toy, and everything should be inside the plastic.",
			// },
			// {
			// 	id: PhotoStyleID.ActionFigureTeacher,
			// 	name: "Teacher",
			// 	prompt: "Draw an action figure toy of a teacher in this photo. The figure should be full figure, with accurate facial features and a professional teaching outfit (e.g., a blazer, skirt or trousers, and glasses). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show 3-5 toy accessories inspired by the teacher's outfit or profession (e.g., a chalkboard, a textbook, a pointer, a coffee mug, or a stack of papers), ensuring no accessories are body parts. This figure should look like a premium toy, and everything should be inside the plastic.",
			// },
			// {
			// 	id: PhotoStyleID.ActionFigurePoliceman,
			// 	name: "Policeman",
			// 	prompt: "Draw an action figure toy of a policeman in this photo. The figure should be full figure, with detailed facial features and a police uniform (e.g., a blue uniform, badge, and duty belt). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show 3-5 toy accessories inspired by the policeman's outfit or profession (e.g., a walkie-talkie, handcuffs, a flashlight, a police hat, or a notepad), ensuring no accessories are body parts. This figure should look like a premium toy, and everything should be inside the plastic.",
			// },
			// {
			// 	id: PhotoStyleID.ActionFigureFirefighter,
			// 	name: "Firefighter",
			// 	prompt: "Draw an action figure toy of a firefighter in this photo. The figure should be full figure, with precise facial features and a firefighter outfit (e.g., a fire-resistant jacket, helmet, and boots). Display the figure in its original blister pack packaging. In the blister pack, next to the figure, show 3-5 toy accessories inspired by the firefighter's outfit or profession (e.g., a fire axe, a hose, a flashlight, an oxygen tank, or a fire extinguisher), ensuring no accessories are body parts. This figure should look like a premium toy, and everything should be inside the plastic.",
			// },
		],
	},
	{
		promptPlaceholder: "Add specific instructions for the Al (e.g., Make it more colorful)",
		category: {
			id: StyleCategoryID.Sketch,
			name: "Sketch",
		},
		style: [
			{
				id: PhotoStyleID.Outline,
				name: "Outline",
				prompt: "Transform the image into black and white outline-solid drawing style",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/outline_short.webp`,
			},
			{
				id: PhotoStyleID.LineArt,
				name: "Line Art",
				prompt: "Transform the image into line art style: A minimalist white and black stylized artwork of the subject , having fewer and thinner lines. The image should depict the subject in a flowing, abstract design using very sleek, continuous line drawing. The background remains white to emphasize the minimalist aesthetic and the elegance of the slim black line art.",
				previewImageShort: `${OSS_URL}/mkt/app/style/cartoon/line_art_short.webp`,
			},
		],
	},
];
export const getStyleType = (categoryId: number, styleId: number): StyleType | null => {
	const photoStyle = getPhotoStyleTypes.find((photoStyle) => photoStyle.category.id === categoryId);
	if (!photoStyle) {
		return null;
	}
	return photoStyle.style.find((style) => style.id === styleId) ?? null;
};
export const getCategoryAndStyleType = (categoryId: number, styleId: number): { category: StyleCategory | null; style: StyleType | null } => {
	const photoStyle = getPhotoStyleTypes.find((photoStyle) => photoStyle.category.id === categoryId);
	if (!photoStyle) {
		return { category: null, style: null };
	}
	const style = photoStyle.style.find((style) => style.id === styleId) ?? null;
	return { category: photoStyle.category, style };
};

export type PhotoCategoryIdAndStyle = {
	categoryId: number;
	style: StyleType;
};
export const getPhotoCategoryIdAndStyle = (categoryId: number, styleId: number): PhotoCategoryIdAndStyle | null => {
	if (categoryId === StyleCategoryID.None) {
		return {
			categoryId: StyleCategoryID.None,
			style: { id: PhotoStyleID.None, name: "Custom" },
		};
	}
	if (styleId === StyleCategoryID.Reference) {
		return {
			categoryId: StyleCategoryID.Reference,
			style: { id: PhotoStyleID.Reference, name: "Reference" },
		};
	}
	const style = getStyleType(categoryId, styleId);
	if (style) {
		return { categoryId: categoryId, style };
	}
	return null;
};
