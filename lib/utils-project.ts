import { MembershipID } from "@/@types/membership-type";
import { ParagraphItem, SpeakerItem } from "@/@types/project/paragraphs-type";
import { MindmapJson } from "@/@types/project/project-note-type";
import { addDays } from "date-fns";
import { formatTime } from "./utils-date";

// export const checkProjectIsExpireWithFileMembership = (fileMembership: number, projectCreateDate: Date) => {
// 	if (fileMembership === MembershipID.Free) {
// 		//免费用户只保存3天
// 		const avaliableDate = addDays(projectCreateDate, 3);
// 		return avaliableDate < new Date();
// 	}
// 	if (fileMembership === MembershipID.Starter) {
// 		// Starter用户保存30天
// 		const avaliableDate = addDays(projectCreateDate, 30);
// 		return avaliableDate < new Date();
// 	}
// 	if (fileMembership === MembershipID.Growth) {
// 		//Growth用户保存180天
// 		const avaliableDate = addDays(projectCreateDate, 180);
// 		return avaliableDate < new Date();
// 	}
// 	return false;
// };

// export const checkProjectIsExpire = (membershipId: number, projectCreateDate: Date) => {
// 	if (membershipId === MembershipID.Free) {
// 		//免费用户只保存3天
// 		const avaliableDate = addDays(projectCreateDate, 3);
// 		return avaliableDate < new Date();
// 	}
// 	if (membershipId === MembershipID.Starter) {
// 		// Starter用户保存30天
// 		const avaliableDate = addDays(projectCreateDate, 30);
// 		return avaliableDate < new Date();
// 	}
// 	if (membershipId === MembershipID.Growth) {
// 		//Growth用户保存180天
// 		const avaliableDate = addDays(projectCreateDate, 180);
// 		return avaliableDate < new Date();
// 	}
// 	return false;
// };

/**
 * Extracts speaker data from an array of paragraphs.
 *
 * @param paragraphs An array of paragraph objects.
 * @returns An array of speaker objects, where each speaker is distinct.
 */
export function extractSpeakers(paragraphs: ParagraphItem[]): SpeakerItem[] {
	const speakers: SpeakerItem[] = [];
	const speakerIds: Set<number> = new Set(); // Use a Set to track unique speaker IDs

	for (const paragraph of paragraphs) {
		if (paragraph.speaker === undefined) {
			continue;
		}

		const speakerId = paragraph.speaker;

		if (!speakerIds.has(speakerId)) {
			speakerIds.add(speakerId);
			speakers.push({
				speakerId: speakerId,
				speakerName: `Speaker ${speakerId}`,
			});
		}
	}

	return speakers;
}

export function getTranscriptWithTimeForLLM(paragraphs: ParagraphItem[]): string {
	const transcriptWithTime: string = paragraphs
		.map((paragraph) => {
			const sentences = paragraph.sentences
				.map((sentence) => {
					// 保留两位小数并向上取整
					const rounded = Math.ceil(sentence.start * 100) / 100;
					return `[${rounded}s]${sentence.text}`;
				})
				.join("\n");
			return sentences;
		})
		.join("\n");

	return transcriptWithTime;
}

export function convertToMarkdown(data: MindmapJson[]): string {
	const result: string[] = [];

	// Find the title (key = 0)
	const title = data.find((item) => item.id === 0);
	if (title) {
		result.push(`# ${title.point}\n`);
	}

	// Group items by parentKey
	const groupedByParent: Record<number, MindmapJson[]> = {};
	data.forEach((item) => {
		if (item.id === 0) return; // Skip the title

		if (!groupedByParent[item.parentId]) {
			groupedByParent[item.parentId] = [];
		}
		groupedByParent[item.parentId].push(item);
	});
	// console.log("groupedByParent", groupedByParent);

	// Process each key level
	function processLevel(parentKey: number, depth: number = 1) {
		const items = groupedByParent[parentKey] || [];

		// Sort items by startSeconds to maintain order
		items.sort((a, b) => a.startSeconds - b.startSeconds);

		for (const item of items) {
			// Determine heading level
			let headingPrefix = "";
			if (depth === 1) {
				headingPrefix = `## (${formatTime(item.startSeconds)})`; // h2
			} else if (depth === 2) {
				headingPrefix = `### (${formatTime(item.startSeconds)})`; // h3
			} else {
				headingPrefix = `${"    ".repeat(depth - 3)}- `;
			}

			result.push(`${headingPrefix}${item.point}\n`);

			// Process children
			if (groupedByParent[item.id]) {
				processLevel(item.id, depth + 1);
			}
		}
	}

	// Start processing from the title's key
	if (title) {
		processLevel(title.id);
	}

	// console.log(result);

	return result.join("");
}
