type LanguageType = {
	value: string;
	name: string;
	nameS?: string;
	splitSpace: string;
	flags: string[];
};
export const getLanguages: LanguageType[] = [
	{
		value: "en",
		name: "English",
		nameS: "English",
		splitSpace: " ",
		flags: ["🇺🇸", "🇬🇧", "🇦🇺", "🇨🇦", "🇳🇿"],
	},
	{
		value: "es",
		name: "Spanish",
		nameS: "Spanish",
		splitSpace: " ",
		flags: ["🇪🇸", "🇲🇽"],
	},
	{
		value: "fr",
		name: "French",
		nameS: "French",
		splitSpace: " ",
		flags: ["🇫🇷", "🇨🇦", "🇧🇪", "🇨🇭"],
	},
	{
		value: "de",
		name: "German",
		nameS: "German",
		splitSpace: " ",
		flags: ["🇩🇪"],
	},
	{
		value: "it",
		name: "Italian",
		nameS: "Italian",
		splitSpace: " ",
		flags: ["🇮🇹"],
	},
	{
		value: "pt",
		name: "Portuguese",
		nameS: "Portuguese",
		splitSpace: " ",
		flags: ["🇵🇹", "🇧🇷"],
	},
	{
		value: "ja",
		name: "Japanese",
		nameS: "Japanese",
		splitSpace: "",
		flags: ["🇯🇵"],
	},
	{
		value: "zh",
		name: "Chinese",
		nameS: "Chinese",
		splitSpace: "",
		flags: ["🇨🇳", "🇸🇬"],
	},
	{
		value: "ko",
		name: "Korean",
		nameS: "Korean",
		splitSpace: "",
		flags: ["🇰🇷"],
	},
	{
		value: "nl",
		name: "Dutch",
		nameS: "Dutch",
		splitSpace: " ",
		flags: ["🇳🇱", "🇧🇪"],
	},
	{
		value: "tr",
		name: "Turkish",
		nameS: "Turkish",
		splitSpace: " ",
		flags: ["🇹🇷"],
	},
	{
		value: "bg",
		name: "Bulgarian",
		splitSpace: " ",
		flags: ["🇧🇬"],
	},
	{
		value: "ca",
		name: "Catalan",
		splitSpace: " ",
		flags: ["🇪🇸"],
	},
	{
		value: "cs",
		name: "Czech",
		splitSpace: " ",
		flags: ["🇨🇿"],
	},
	{
		value: "da",
		name: "Danish",
		splitSpace: " ",
		flags: ["🇩🇰"],
	},
	// {
	// 	value: "en-US",
	// 	name: "English (United States)",
	// },
	// {
	// 	value: "en-AU",
	// 	name: "English (Australia)",
	// },
	// {
	// 	value: "en-GB",
	// 	name: "English (United Kingdom)",
	// },
	// {
	// 	value: "en-NZ",
	// 	name: "English (New Zealand)",
	// },
	// {
	// 	value: "en-IN",
	// 	name: "English (India)",
	// },
	{
		value: "et",
		name: "Estonian",
		splitSpace: " ",
		flags: ["🇪🇪"],
	},
	{
		value: "fi",
		name: "Finnish",
		splitSpace: " ",
		flags: ["🇫🇮"],
	},
	// {
	// 	value: "fr-CA",
	// 	name: "French (Canada)",
	// },
	// {
	// 	value: "de-CH",
	// 	name: "German (Switzerland)",
	// },
	{
		value: "el",
		name: "Greek",
		splitSpace: " ",
		flags: ["🇬🇷"],
	},
	{
		value: "hi",
		name: "Hindi",
		splitSpace: " ",
		flags: ["🇮🇳"],
	},
	{
		value: "hu",
		name: "Hungarian",
		splitSpace: " ",
		flags: ["🇭🇺"],
	},
	{
		value: "id",
		name: "Indonesian",
		splitSpace: " ",
		flags: ["🇮🇩"],
	},
	{
		value: "lv",
		name: "Latvian",
		splitSpace: " ",
		flags: ["🇱🇻"],
	},
	{
		value: "lt",
		name: "Lithuanian",
		splitSpace: " ",
		flags: ["🇱🇹"],
	},
	{
		value: "ms",
		name: "Malay",
		splitSpace: " ",
		flags: ["🇲🇾"],
	},
	{
		value: "no",
		name: "Norwegian",
		splitSpace: " ",
		flags: ["🇳🇴"],
	},
	{
		value: "pl",
		name: "Polish",
		splitSpace: " ",
		flags: ["🇵🇱"],
	},
	// {
	// 	value: "pt-BR",
	// 	name: "Portuguese (Brazil)",
	// },
	// {
	// 	value: "pt-PT",
	// 	name: "Portuguese (Portugal)",
	// },
	{
		value: "ro",
		name: "Romanian",
		splitSpace: " ",
		flags: ["🇷🇴"],
	},
	{
		value: "ru",
		name: "Russian",
		splitSpace: " ",
		flags: ["🇷🇺"],
	},
	{
		value: "sk",
		name: "Slovak",
		splitSpace: " ",
		flags: ["🇸🇰"],
	},
	// {
	// 	value: "es-419",
	// 	name: "Spanish (Latin America)",
	// },
	{
		value: "sv",
		name: "Swedish",
		splitSpace: " ",
		flags: ["🇸🇪"],
	},
	{
		value: "th",
		name: "Thai",
		splitSpace: "",
		flags: ["🇹🇭"],
	},
	{
		value: "uk",
		name: "Ukrainian",
		splitSpace: " ",
		flags: ["🇺🇦"],
	},
	{
		value: "vi",
		name: "Vietnamese",
		splitSpace: " ",
		flags: ["🇻🇳"],
	},
];

export const getLanguageName = (value: string): string | undefined => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.name;
};
export const getLanguageFlag = (value: string): string | undefined => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.flags[0];
};

export const getLanguageSplitSpace = (value: string): string => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.splitSpace ?? " ";
};
export const getLanguageSplitSpaceLength = (value: string): number => {
	const language = getLanguages.find((lang) => lang.value === value);
	return language?.splitSpace.length ?? 0;
};
