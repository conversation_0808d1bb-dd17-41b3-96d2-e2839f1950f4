import { spawn } from "child_process";
import { createReadStream, createWriteStream, promises as fs } from "fs";
import { pipeline } from "stream/promises";
import { tmpdir } from "os";
import { join } from "path";
import { getUUIDString } from "../utils";
import { getFileExtension, getFileName, isVideoFileByType } from "./utils-file";

/**
 * Convert video file to MP3 audio using ffmpeg
 * Uses streaming to handle large files efficiently
 */
export async function convertVideoToAudio(inputBuffer: Buffer, originalFilename: string): Promise<Buffer> {
	const tempDir = tmpdir();
	const inputId = getUUIDString();
	const outputId = getUUIDString();

	const inputPath = join(tempDir, `${inputId}.${getFileExtension(originalFilename)}`);
	const outputPath = join(tempDir, `${outputId}.mp3`);

	try {
		// Write input buffer to temporary file
		await fs.writeFile(inputPath, inputBuffer);

		// Convert video to audio using ffmpeg
		await new Promise<void>((resolve, reject) => {
			const ffmpeg = spawn("ffmpeg", [
				"-i",
				inputPath,
				"-vn", // No video
				"-acodec",
				"libmp3lame", // Use MP3 codec
				"-ab",
				"128k", // Audio bitrate
				"-ar",
				"44100", // Sample rate
				"-y", // Overwrite output file
				outputPath,
			]);

			let errorOutput = "";

			ffmpeg.stderr.on("data", (data) => {
				errorOutput += data.toString();
			});

			ffmpeg.on("close", (code) => {
				if (code === 0) {
					resolve();
				} else {
					reject(new Error(`FFmpeg process exited with code ${code}: ${errorOutput}`));
				}
			});

			ffmpeg.on("error", (error) => {
				if ((error as any).code === "ENOENT") {
					reject(new Error(`FFmpeg not found. Please ensure FFmpeg is installed in the system. Original error: ${error.message}`));
				} else {
					reject(new Error(`FFmpeg spawn error: ${error.message}`));
				}
			});
		});

		// Read the converted audio file
		const audioBuffer = await fs.readFile(outputPath);
		return audioBuffer;
	} finally {
		// Clean up temporary files
		try {
			await fs.unlink(inputPath);
		} catch (error) {
			console.warn("Failed to clean up input file:", error);
		}

		try {
			await fs.unlink(outputPath);
		} catch (error) {
			console.warn("Failed to clean up output file:", error);
		}
	}
}

/**
 * Process uploaded file - convert video to audio if needed
 * Returns processed buffer and final filename
 */
export async function processMediaFile(
	fileBuffer: Buffer,
	originalFilename: string,
	fileType: string,
): Promise<{ buffer: Buffer; filename: string; contentType: string }> {
	if (isVideoFileByType(fileType)) {
		// Convert video to audio
		const audioBuffer = await convertVideoToAudio(fileBuffer, originalFilename);
		const audioFilename = `${getFileName(originalFilename)}.mp3`;

		return {
			buffer: audioBuffer,
			filename: audioFilename,
			contentType: "audio/mpeg",
		};
	} else {
		// Audio file - return as is
		const extension = getFileExtension(originalFilename);
		let contentType = "audio/mpeg"; // Default to MP3

		// Set appropriate content type based on extension
		switch (extension.toLowerCase()) {
			case "wav":
				contentType = "audio/wav";
				break;
			case "ogg":
				contentType = "audio/ogg";
				break;
			case "aac":
				contentType = "audio/aac";
				break;
			case "flac":
				contentType = "audio/flac";
				break;
			case "m4a":
				contentType = "audio/mp4";
				break;
			default:
				contentType = "audio/mpeg";
		}

		return {
			buffer: fileBuffer,
			filename: originalFilename,
			contentType,
		};
	}
}

/**
 * Stream-based file reading to handle large files efficiently
 */
export async function readFileInChunks(file: File, chunkSize: number = 1024 * 1024): Promise<Buffer> {
	const chunks: Buffer[] = [];
	let offset = 0;

	while (offset < file.size) {
		const chunk = file.slice(offset, offset + chunkSize);
		const arrayBuffer = await chunk.arrayBuffer();
		chunks.push(Buffer.from(arrayBuffer));
		offset += chunkSize;
	}

	return Buffer.concat(chunks);
}
