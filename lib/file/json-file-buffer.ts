export function createJSONFileBuffer(jsonData: any): ArrayBuffer {
	// 将 JSON 数据转换为字符串
	const jsonString = JSON.stringify(jsonData);
	// 将 JSON 字符串编码为 UTF-8 字节流
	const encoder = new TextEncoder();
	const byteArray = encoder.encode(jsonString);

	// 将字节流转换为 ArrayBuffer
	return byteArray.buffer as ArrayBuffer;
	// // 使用 TextEncoder 编码字符串为字节数组
	// const encoder = new TextEncoder();
	// const encodedData = encoder.encode(jsonString);

	// // 返回 ArrayBuffer
	// return encodedData.buffer;
}

export function getJSONFromBuffer(arrayBuffer: ArrayBuffer): any {
	const decoder = new TextDecoder("utf-8");
	const jsonString = decoder.decode(arrayBuffer);

	// 将字符串解析为 JSON 对象
	try {
		const jsonObject = JSON.parse(jsonString);
		return jsonObject;
	} catch (e) {
		console.error("JSON 解析失败:", e);
	}
}
