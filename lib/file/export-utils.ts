"use client";

import { saveAs } from "file-saver";
import { Document, Packer, Paragraph, TextRun } from "docx";
import { PDFDocument, PDFFont, PageSizes, StandardFonts, rgb } from "pdf-lib";
import fontkit from "@pdf-lib/fontkit";
import { OSS_URL } from "../constants";
/**
 * Format time in seconds to MM:SS or HH:MM:SS format
 */
function formatTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);

	if (hours > 0) {
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
	}
	return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
}

/**
 * Export sentences as plain text (.txt)
 */
export function exportAsTxt(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.txt`);
}

/**
 * Export sentences as Markdown (.md)
 */
export function exportAsMarkdown(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/markdown;charset=utf-8" });
	saveAs(blob, `${filename}.md`);
}

/**
 * Export sentences as SRT subtitle format (.srt)
 */
export function exportAsSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

/**
 * Format time for SRT format (HH:MM:SS,mmm)
 */
function formatSrtTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);
	const milliseconds = Math.floor((seconds % 1) * 1000);

	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")},${milliseconds.toString().padStart(3, "0")}`;
}

/**
 * Export sentences as PDF (.pdf)
 */
const exportToPDF = async (sentences: SentenceData[], languageCode: string, filename: string = "document"): Promise<void> => {
	const fontSize = 12;
	const lineHeight = fontSize * 1.5;
	const margin = 50;

	const content = sentences.map((sentence) => sentence.text).join(" ");

	// 创建新的PDF文档
	const pdfDoc = await PDFDocument.create();

	// 嵌入字体
	let font = await pdfDoc.embedFont(StandardFonts.Helvetica);
	let unicodeFont = false;
	let fontBytes;
	console.log("languageCode:", languageCode);
	switch (languageCode) {
		case "zh":
			unicodeFont = true;
			fontBytes = (await fetch(`${OSS_URL}/fonts/NotoSansSC-Regular.ttf`).then((res) => res.arrayBuffer())) as ArrayBuffer;
			break;
		case "ja":
			unicodeFont = true;
			fontBytes = (await fetch(`${OSS_URL}/fonts/NotoSansJP-Regular.ttf`).then((res) => res.arrayBuffer())) as ArrayBuffer;
			break;
		case "ko":
			unicodeFont = true;
			fontBytes = (await fetch(`${OSS_URL}/fonts/NotoSansKR-Regular.ttf`).then((res) => res.arrayBuffer())) as ArrayBuffer;
			break;
		case "ar":
			unicodeFont = true;
			fontBytes = (await fetch(`${OSS_URL}/fonts/NotoSansArabic-Regular.ttf`).then((res) => res.arrayBuffer())) as ArrayBuffer;
			break;
		case "th":
			unicodeFont = true;
			fontBytes = (await fetch(`${OSS_URL}/fonts/NotoSansThai-Regular.ttf`).then((res) => res.arrayBuffer())) as ArrayBuffer;
			break;
		default:
			break;
	}
	if (unicodeFont) {
		pdfDoc.registerFontkit(fontkit);
		font = await pdfDoc.embedFont(fontBytes!);
	}

	// 页面设置
	let currentPage = pdfDoc.addPage(PageSizes.A4);
	const { width, height } = currentPage.getSize();
	const maxWidth = width - 2 * margin;
	const maxLinesPerPage = Math.floor((height - 2 * margin) / lineHeight);

	// 处理文本换行
	const wrappedLines = wrapText(content, maxWidth, font, fontSize);

	let yPosition = height - margin;
	let lineCount = 0;

	// 逐行添加文本
	for (const line of wrappedLines) {
		// 检查是否需要新页面
		if (lineCount >= maxLinesPerPage) {
			currentPage = pdfDoc.addPage([width, height]);
			yPosition = height - margin;
			lineCount = 0;
		}

		// 绘制文本
		currentPage.drawText(line, {
			x: margin,
			y: yPosition,
			size: fontSize,
			font: font,
			color: rgb(0, 0, 0),
		});

		yPosition -= lineHeight;
		lineCount++;
	}

	// 生成PDF字节数组
	const pdfBytes = (await pdfDoc.save()) as BlobPart;

	// 创建下载链接
	const blob = new Blob([pdfBytes], { type: "application/pdf" });
	const url = URL.createObjectURL(blob);

	// 触发下载
	const link = document.createElement("a");
	link.href = url;
	link.download = filename;
	link.click();

	// 清理URL对象
	URL.revokeObjectURL(url);
};
const wrapText = (text: string, maxWidth: number, font: PDFFont, fontSize: number): string[] => {
	const words = text.replace(/\n/g, " \n ").split(" ");
	const lines: string[] = [];
	let currentLine = "";

	for (const word of words) {
		if (word === "\n") {
			lines.push(currentLine.trim());
			currentLine = "";
			continue;
		}

		const testLine = currentLine ? `${currentLine} ${word}` : word;
		const testWidth = font.widthOfTextAtSize(testLine, fontSize);

		if (testWidth <= maxWidth) {
			currentLine = testLine;
		} else {
			if (currentLine) {
				lines.push(currentLine.trim());
				currentLine = word;
			} else {
				// 单词太长，强制分割
				lines.push(word);
			}
		}
	}

	if (currentLine) {
		lines.push(currentLine.trim());
	}

	return lines.filter((line) => line.length > 0);
};

/**
 * Export sentences as Word document (.docx)
 */
export async function exportAsDocx(sentences: SentenceData[], filename: string = "Transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");

	const doc = new Document({
		sections: [
			{
				properties: {},
				children: [
					new Paragraph({
						children: [
							new TextRun({
								text: filename,
								bold: true,
								size: 32,
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: "",
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: content,
								size: 24,
							}),
						],
					}),
				],
			},
		],
	});

	const buffer = (await Packer.toBuffer(doc)) as BlobPart;
	const blob = new Blob([buffer], {
		type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	});
	saveAs(blob, `${filename}.docx`);
}

/**
 * Export sentences with timestamps as detailed SRT
 */
export function exportAsDetailedSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

export type ExportFormat = "txt" | "pdf" | "md" | "srt" | "docx";

/**
 * Main export function that handles all formats
 */
export async function exportTranscript(sentences: SentenceData[], format: ExportFormat, languageCode: string, filename: string = "transcript") {
	switch (format) {
		case "txt":
			exportAsTxt(sentences, filename);
			break;
		case "pdf":
			await exportToPDF(sentences, languageCode, filename);
			break;
		case "md":
			exportAsMarkdown(sentences, filename);
			break;
		case "srt":
			exportAsSrt(sentences, filename);
			break;
		case "docx":
			await exportAsDocx(sentences, filename);
			break;
		default:
			throw new Error(`Unsupported export format: ${format}`);
	}
}
