import { MembershipID, membershipMapping } from "@/@types/membership-type";
import {
	SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH,
	SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR,
	SUBSCRIPTION_PRODUCT_ID_PRO_MONTH,
	SUBSCRIPTION_PRODUCT_ID_PRO_YEAR,
	ORDER_PRODUCT_ID_1,
	getOrderProductInfo,
	ORDER_PRODUCT_ID_2,
	ORDER_PRODUCT_ID_3,
} from "@/lib/utils-membership";

export type PricingPlan = {
	id: number;
	title: string;
	description?: string;
	free: boolean;
	productId?: {
		monthly: string;
		yearly: string;
	};
	price: {
		monthly: number;
		monthForYearly: number;
		yearly: number;
	};
	currency: {
		code: string;
		symbol: string;
	};
	duration: string;
	badge?: string;
	buttonHighlighted: boolean;
	features?: {
		description: string;
		tips?: string;
	}[];
	unFeatures?: {
		description: string;
		tips?: string;
	}[];
};

export const pricingPlans: PricingPlan[] = [
	{
		id: MembershipID.Starter,
		title: membershipMapping[MembershipID.Starter].name,
		free: false,
		productId: { monthly: SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH, yearly: SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR },
		price: { monthly: 10, monthForYearly: 6, yearly: 72 },
		currency: { code: "USD", symbol: "$" },
		duration: "per month",
		buttonHighlighted: false,
		features: [
			// {
			// 	description: `${membershipMapping[MembershipID.Starter].credits} credits / month`,
			// 	tips: "1 credit = 1 min of AI transcription",
			// },
			{
				description: `${membershipMapping[MembershipID.Starter].credits} transcription minutes / month`,
			},
			{ description: "Up to 2 hours / file" },
			{ description: "100+ languages" },
			{ description: "Speaker recongnition" },
			{ description: "Transcripts export", tips: "Export as .txt, .pdf, .md, .srt, .docx" },
			// { description: "History storage(coming soon)", tips: "30 days of transcription history" },
			{ description: "Access to priority queue" },
			{ description: "24 Hour support Time" },
			{ description: "Cancel anytime" },
		],
		// unFeatures: ["AI Plus translation"],
	},
	{
		id: MembershipID.Pro,
		title: membershipMapping[MembershipID.Pro].name,
		free: false,
		productId: { monthly: SUBSCRIPTION_PRODUCT_ID_PRO_MONTH, yearly: SUBSCRIPTION_PRODUCT_ID_PRO_YEAR },
		price: { monthly: 20, monthForYearly: 12, yearly: 144 },
		currency: { code: "USD", symbol: "$" },
		duration: "per month",
		badge: "Most Popular",
		buttonHighlighted: true,
		features: [
			{
				// description: `${membershipMapping[MembershipID.Growth].credits} credits / month`,
				// tips: "1 credit = 1 min of AI transcription",
				description: "Unlimited transcription minutes",
				// tips: "Unlimited AI transcription",
			},
			{ description: "Up to 5 hours / file" },
			// { description: "AI transcription" },
			// { description: "AI insights(Summary, Mindmap, etc.)" },
			{ description: "100+ languages" },
			{ description: "Speaker recongnition" },
			{ description: "Transcripts export", tips: "Export as .txt, .pdf, .md, .srt, .docx" },
			// { description: "History storage(coming soon)", tips: "30 days of transcription history" },
			{ description: "Access to priority queue" },
			{ description: "Priority support" },
			{ description: "Cancel anytime" },
			{ description: "Early access to new features" },
		],
		unFeatures: [],
	},
	{
		id: MembershipID.Free,
		title: membershipMapping[MembershipID.Free].name,
		free: true,
		price: { monthly: 0, monthForYearly: 0, yearly: 0 },
		currency: { code: "USD", symbol: "$" },
		duration: "per month",
		buttonHighlighted: false,
		features: [
			// {
			// 	description: `${membershipMapping[MembershipID.Free].credits} credits / month`,
			// 	tips: "1 credit = 1 min of AI transcription",
			// },
			{
				description: "3 transcripts / day",
				tips: "Transcribe 3 files for free every day.",
			},
			{ description: "Up to 30 minutes / file" },
			{ description: "100+ languages" },
			// { description: "AI transcription" },
			// { description: "AI insights(Summary, Mindmap, etc.)" },
		],
		unFeatures: [
			{ description: "Speaker recongnition" },
			{ description: "Transcripts export", tips: "Export as .txt, .pdf, .md, .srt, .docx" },
			// { description: "History storage(coming soon)", tips: "30 days of transcription history" },
		],
	},
];

export type PricingOnetime = {
	free?: boolean;
	credits: number;
	description?: string;
	productId: string;
	price: number;
	currency: {
		code: string;
		symbol: string;
	};
	badge?: string;
	checkoutLink?: string;
	features?: {
		description: string;
		tips?: string;
	}[];
	unFeatures?: string[];
};

export const pricingOnetime: PricingOnetime[] = [
	{
		free: true,
		credits: membershipMapping[MembershipID.Free].credits!,
		productId: "free",
		price: 0,
		currency: { code: "USD", symbol: "$" },
		features: [
			{ description: `${membershipMapping[MembershipID.Free].credits} images` },
			{ description: "15+ style presets" },
			{ description: "With watermark" },
			{ description: "Only for personal use" },
			{ description: "Public generation" },
		],
	},
	{
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_1)?.credits!,
		productId: ORDER_PRODUCT_ID_1,
		price: 5,
		currency: { code: "USD", symbol: "$" },
		// checkoutLink:
		// 	process.env.NODE_ENV === "production"
		// 		? "https://buy.polar.sh/polar_cl_lLnxoehJrSkOQHFIh5Kgsj5A9nGVofIWlyqQ63G8uwC"
		// 		: "https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_YcmeNf8vcf1bWjgdGOCE2EiHDSW7rm5t7u4HW13nFsr/redirect",
		features: [
			{ description: `${getOrderProductInfo(ORDER_PRODUCT_ID_1)?.credits} images, valid for 1 month` },
			{ description: "20+ style presets" },
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			// { description: "Preset styles" },
			{ description: "Commercial use" },
			{ description: "Private generation" },
		],
	},
	{
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_2)?.credits!,
		productId: ORDER_PRODUCT_ID_2,
		price: 10,
		currency: { code: "USD", symbol: "$" },
		badge: "Most Popular",
		// checkoutLink:
		// 	process.env.NODE_ENV === "production"
		// 		? "https://buy.polar.sh/polar_cl_0gSfR8EI5Se0ib1npiNrjLGNec1UeUZobPwR81PFYuI"
		// 		: "https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_R9WX9jdkabDk0x49Jee0xJAnnK7xUqIHYKjfy16hhCJ/redirect",
		features: [
			{ description: `${getOrderProductInfo(ORDER_PRODUCT_ID_2)?.credits} images, valid for 1 month` },
			{ description: "20+ style presets" },
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			// { description: "Preset styles" },
			{ description: "Commercial use" },
			{ description: "Private generation" },
		],
	},
	{
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_3)?.credits!,
		productId: ORDER_PRODUCT_ID_3,
		price: 15,
		currency: { code: "USD", symbol: "$" },
		// checkoutLink:
		// 	process.env.NODE_ENV === "production"
		// 		? "https://buy.polar.sh/polar_cl_Takkcpvho54qFFnceU3TDwNWkFNAnkzW9oBYu2LjDgw"
		// 		: "https://sandbox-api.polar.sh/v1/checkout-links/polar_cl_mHa8t78EUbxkOMhH1b8yKLfzsFBpLn1japsqF0LqEnu/redirect",
		features: [
			{ description: `${getOrderProductInfo(ORDER_PRODUCT_ID_3)?.credits} images, valid for 1 month` },
			{ description: "20+ style presets" },
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			// { description: "Preset styles" },
			{ description: "Commercial use" },
			{ description: "Private generation" },
		],
	},
];

export const pricingOnetimeFeatures: {
	description: string;
	tips?: string;
}[] = [
	{ description: "High Resolution" },
	{ description: "Priority Support" },
	{ description: "30-day Validity" },
	{ description: "More Preset Styles (Coming soon)" },
];
