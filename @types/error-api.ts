import { NextResponse } from "next/server";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r, Credits402Error, FreeLimitError } from "@/@types/error";
import { notifyDevEvent } from "@/server/dev-notify.server";

export function handleApiError(error: any, eventName: string) {
	console.error("API error:", error);

	const errorClasses = [AuthError, ParamsError, Credits402Error, FreeLimitError];
	const knownError = errorClasses.find((errorClass) => error instanceof errorClass);

	if (knownError) {
		return NextResponse.json({
			status: error.statusCode,
			message: error.message,
		});
	}

	notifyDevEvent(eventName, "Error", error.message, null);
	return NextResponse.json({ status: 500, error: error.message });
}
