// ==================== Membership ID ====================
export enum MembershipID {
	Free = 0,
	Starter = 10,
	// Growth = 11,
	Pro = 12,
}

// ==================== Membership ID Type ====================
export interface MembershipType {
	id: number;
	name: string;
	credits?: number;
}

export const membershipMapping: Record<MembershipID, MembershipType> = {
	[MembershipID.Free]: {
		id: MembershipID.Free,
		name: "Free",
		credits: 0,
	},
	[MembershipID.Starter]: {
		id: MembershipID.Starter,
		name: "Starter",
		credits: 600,
	},
	[MembershipID.Pro]: {
		id: MembershipID.Pro,
		name: "Pro",
		credits: 6000,
	},
};

// ==================== Membership Period Type ====================
export interface MembershipPeriodType {
	value: "none" | "month" | "year";
	name: "None" | "Monthly" | "Yearly";
}

export const MembershipPeriodNone: MembershipPeriodType = {
	value: "none",
	name: "None",
};

export const MembershipPeriodMonth: MembershipPeriodType = {
	value: "month",
	name: "Monthly",
};
export const MembershipPeriodYear: MembershipPeriodType = {
	value: "year",
	name: "Yearly",
};
