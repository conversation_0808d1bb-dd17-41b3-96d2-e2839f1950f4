type Badge = {
	href: string;
	src: string;
	alt: string;
};

export const footerBadges: Badge[] = [
	// {
	// 	href: "https://theresanaiforthat.com/ai/s/",
	// 	src: "https://media.theresanaiforthat.com/featured5.png",
	// 	alt: "Pixcribe Spotlight on theresanaiforthat.com",
	// },
	{
		// href: "https://code.market?code.market=verified",
		href: "https://code.market/product/youstylize-photos-to-magic-studio-ghibli-style-art",
		src: "https://code.market/assets/manage-product/featured-logo-bright.svg",
		alt: "YouStylize Spotlight on ai tools code.market",
	},
	{
		href: "https://startupfa.me/s/youstylize?utm_source=youstylize.com",
		src: "https://startupfa.me/badges/featured-badge.webp",
		alt: "Featured on Startup Fame",
	},
	{
		href: "https://dang.ai/",
		src: "https://cdn.prod.website-files.com/63d8afd87da01fb58ea3fbcb/6487e2868c6c8f93b4828827_dang-badge.png",
		alt: "Dang.ai",
	},
	{
		href: "https://similarlabs.com/?ref=embed",
		src: "https://similarlabs.com/similarlabs-embed-badge-dark.svg",
		alt: "SimilarLabs Embed Badge",
	},
	{
		href: "https://twelve.tools",
		src: "https://twelve.tools/badge2-white.svg",
		alt: "Featured on Twelve Tools",
	},
	{
		href: "https://wavel.io/",
		src: "https://wavel.io/embed/logo-card/Wavel-embed-badge-with-light-bg.png",
		alt: "Featured on Wavel",
	},
	{
		href: "https://topfreeaitools.com/ai/youstylize",
		src: "https://ff65dcf08ebd5eb1c022b44dd88016ac.cdn.bubble.io/f1724746116087x632750678197528400/badge%20white.png",
		alt: "Featured on Top Free AI Tools",
	},
	// {
	// 	href: "https://sprunkid.com",
	// 	src: "https://sprunkid.com/_next/image?url=%2Flogo.png&w=64&q=75",
	// 	alt: "Sprunkid",
	// },
];
