export interface ParagraphItem {
	start: number;
	end: number;
	sentences: {
		start: number;
		end: number;
		text: string;
	}[];
	speaker?: number;
}

export interface SpeakerItem {
	speakerId: number;
	speakerName: string;
}

export const getSpeakerIconColor = (speakerId: number) => {
	switch (speakerId) {
		// case 0:
		// 	return "#000000";
		case 0:
			return "#3b82f6"; //blue
		case 1:
			return "#6b7280"; //gray
		case 2:
			return "#ec4899"; //pink
		case 3:
			return "#f59e0b"; //amber(yellow orange)
		case 4:
			return "#6366f1"; //indigo(blue violet)
		case 5:
			return "#f97316"; //orange
		case 6:
			return "#22c55e"; //green
		case 7:
			return "#ef4444"; //red
		default:
			return "#000000";
	}
};
