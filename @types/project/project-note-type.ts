import { z } from "zod";

export enum ProjectNoteType {
	Summary = 0,
	Mindmap = 1,
}

// export const mindmapSchema = z.object({
// 	startSeconds: z.number().describe("The start time of the point in seconds"),
// 	point: z.string().min(1, "Point cannot be empty").max(1000, "Point is too long").describe("A key point from the transcript"),
// });
export const mindmapSchema = z.object({
	id: z.number().describe("The unique key number of the point. If it's the title, the key is 0"),
	parentId: z.number().describe("The parent key number of the point. Only the title's parentId equals to the id"),
	startSeconds: z.number().describe("The start time of the point in seconds"),
	point: z.string().min(1, "Point cannot be empty").max(1000, "Point is too long").describe("A key point from the transcript"),
});

export type MindmapJson = z.infer<typeof mindmapSchema>;
