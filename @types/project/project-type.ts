export interface ProjectHead {
	uid: string;
	fileName: string;
	duration: number;
	language: string | null;
	status: number;
	share: number;
	createdAt: Date;
	isTranscriptEmpty: boolean;
	fileUrlLocal: string | null;
	fileUrlTemp: string | null;
}

export enum ProjectStatus {
	None = 0,
	PreparingFile = 1,
	UploadingFile = 11,
	QueuedForTranscription = 21,
	TranscriptionInProgress = 22,
	TranscriptionCompleted = 31,
	TranscriptionFailed = 32,
	Expire = 400,
}
export const getProjectStatusText = (status: number) => {
	switch (status) {
		case ProjectStatus.PreparingFile:
			return "Extracting audio...";
		// return "Preparing file...";
		case ProjectStatus.UploadingFile:
			return "Uploading...";
		case ProjectStatus.QueuedForTranscription:
			return "Creating task...";
		case ProjectStatus.TranscriptionInProgress:
			return "Transcribing...";
		case ProjectStatus.TranscriptionCompleted:
			return "Transcription completed 🎉";
		case ProjectStatus.TranscriptionFailed:
			return "Transcription failed, please try again or contact support";
		default:
			return "";
	}
};
