import { Subscription } from "@/@types/subscription";
import { create } from "zustand";

type userPlanOpenStore = {
	userPlanBoxOpen: boolean;
	userSubscription: Subscription | null;
	setUserPlanBoxOpen: (isOpen: boolean) => void;
	setUserSubscription: (subscription: Subscription | null) => void;
};
export const useUserPlanBoxOpenStore = create<userPlanOpenStore>((set) => {
	return {
		userPlanBoxOpen: false,
		userSubscription: null,
		setUserPlanBoxOpen: (isOpen: boolean) => {
			set({ userPlanBoxOpen: isOpen });
		},
		setUserSubscription: (userSubscription: Subscription | null) => {
			set({ userSubscription });
		},
	};
});
