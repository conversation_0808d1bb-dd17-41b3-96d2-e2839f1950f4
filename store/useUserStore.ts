import { create } from "zustand";
import { UserInfoDB } from "@/@types/user";
import { ofetch } from "ofetch";
import { handleError } from "@/@types/error";
import { userHasPaid } from "@/lib/utils-user";

type UserStore = {
	user: UserInfoDB | null;
	isLoaded: boolean;
	hasPaid: boolean;
	setUser: (user: UserInfoDB | null) => void;
	refreshUser: () => Promise<void>;
};
export const useUserStore = create<UserStore>((set) => {
	return {
		user: null,
		isLoaded: false,
		hasPaid: false,
		setUser: (user: UserInfoDB | null) => {
			const hasPaid = user ? userHasPaid(user.membershipId, user.creditOneTimeEndsAt) : false;
			set({
				user: user,
				isLoaded: true,
				hasPaid: hasPaid,
			});
		},
		refreshUser: async () => {
			const { status, message, userInfo } = await ofetch("/api/v1/user", {
				method: "POST",
				body: {
					type: "credits",
				},
			});
			handleError(status, message);
			const hasPaid = userInfo ? userHasPaid(userInfo.membershipId, userInfo.creditOneTimeEndsAt) : false;
			set({ user: userInfo, hasPaid: hasPaid });
		},
	};
});
