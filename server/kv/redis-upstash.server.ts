import { Redis } from "@upstash/redis";
import { Ratelimit } from "@upstash/ratelimit"; // for deno: see above

export const redisClient = new Redis({
	url: process.env.UPSTASH_REDIS_REST_URL!,
	token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});
const ratelimit = new Ratelimit({
	redis: redisClient,
	limiter: Ratelimit.slidingWindow(3, "1 d"),
	analytics: true,
	/**
	 * Optional prefix for the keys used in redis. This is useful if you want to share a redis
	 * instance with other applications and want to avoid key collisions. The default prefix is
	 * "@upstash/ratelimit"
	 */
	prefix: "@upstash/ratelimit",
});

export async function getValue(key: string) {
	return await redisClient.get(key);
}

export async function setValue(key: string, value: string, ttlSeconds?: number) {
	if (ttlSeconds) {
		await redisClient.set(key, value, { ex: ttlSeconds });
	} else {
		await redisClient.set(key, value);
	}
}

export async function deleteValue(key: string) {
	await redisClient.del(key);
}

export async function checkFreeUserTranscribeLimit(userId: string): Promise<boolean> {
	const { success } = await ratelimit.limit(userId);
	return success;
}
