import { getUUIDString } from "@/lib/utils";
import { sqliteTable, integer, text, index, uniqueIndex, real } from "drizzle-orm/sqlite-core";

// ==============user====================
export const userSchema = sqliteTable("user", {
	pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	id: text("uid").unique().notNull(),
	name: text("name").notNull(),
	email: text("email").notNull().unique(),
	emailVerified: integer("email_verified", { mode: "boolean" }).default(false),
	image: text("image"),

	membershipId: integer("membership_id", { mode: "number" }).default(0).notNull(),
	membershipFormatted: text("membership_formatted").default("Free").notNull(),

	creditFree: integer("credit_free").default(0).notNull(),
	creditFreeEndsAt: integer("credit_free_ends_at", { mode: "timestamp_ms" }),
	creditOneTime: integer("credit_onetime").default(0).notNull(),
	creditOneTimeEndsAt: integer("credit_onetime_ends_at", { mode: "timestamp_ms" }),
	creditSubscription: integer("credit_sub").default(0).notNull(),
	creditSubscriptionEndsAt: integer("credit_sub_ends_at", { mode: "timestamp_ms" }),

	subscriptionId: text("sub_id"),
	subscriptionPeriod: text("sub_period").default("none"), // none, month, year
	subscriptionInvoiceEndsAt: integer("sub_invoice_ends_at", { mode: "timestamp_ms" }),
	subscriptionExpireAt: integer("sub_expire_at", { mode: "timestamp_ms" }),

	isDeleted: integer("is_deleted", { mode: "boolean" }).default(false).notNull(),
	ban: integer("ban", { mode: "boolean" }).default(false).notNull(),
	countryCode: text("country_code"),

	createdAt: integer("created_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date()),
	updatedAt: integer("updated_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date()),
});
export type User = typeof userSchema.$inferSelect; // return type when queried
export type NewUser = typeof userSchema.$inferInsert; // insert type

// ============== better-auth ====================
export const accountSchema = sqliteTable(
	"better_auth_account",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		providerId: text("provider_id").notNull(),
		accountId: text("account_id").notNull(),

		accessToken: text("access_token"),
		refreshToken: text("refresh_token"),
		accessTokenExpiresAt: integer("access_token_expires_at", { mode: "timestamp_ms" }),
		refreshTokenExpiresAt: integer("refresh_token_expires_at", { mode: "timestamp_ms" }),

		scope: text("scope"),
		idToken: text("id_token"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("better_auth_account_user_uid_index").on(table.userId),
		uniqueIndex("better_auth_account_provider_unique").on(table.accountId, table.providerId),
	],
);
export type Account = typeof accountSchema.$inferSelect;
export type NewAccount = typeof accountSchema.$inferInsert;

export const sessionSchema = sqliteTable(
	"better_auth_session",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		token: text("token").notNull(),
		expiresAt: integer("expires_at", { mode: "timestamp_ms" }).notNull(),
		ipAddress: text("ip_address"),
		userAgent: text("user_agent"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("better_auth_session_token_index").on(table.token)],
);
export type Session = typeof sessionSchema.$inferSelect;
export type NewSession = typeof sessionSchema.$inferInsert;

export const verificationSchema = sqliteTable(
	"better_auth_verification",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		identifier: text("identifier").notNull(),
		value: text("value").notNull(),
		expiresAt: integer("expires_at", { mode: "timestamp_ms" }).notNull(),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("better_auth_verification_identifier_index").on(table.identifier),
		index("better_auth_verification_expires_at_index").on(table.expiresAt),
	],
);
export type Verification = typeof verificationSchema.$inferSelect;
export type NewVerification = typeof verificationSchema.$inferInsert;

// ==============transcribe task ===============
export const transcribeTaskSchema = sqliteTable(
	"transcribe_task",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),
		platform: text("platform").notNull(),
		requestId: text("request_id").notNull(),

		status: integer("status", { mode: "number" }).default(0).notNull(),

		fileName: text("file_name").notNull(),
		fileUrlTemp: text("file_url_temp"),
		fileUrlLocal: text("file_url_local"), // file URL entered by users

		duration: real("duration").default(0).notNull(), // In seconds
		language: text("language").notNull(), // In ISO 639-1 format, eg: en
		speaker: integer("speaker", { mode: "number" }), // null: not set, 0: auto, >1: the number of speakers

		creditsSources: text("credits_source"),
		ip: text("ip"),

		projectUid: text("project_uid"),

		remark: text("remark"),
		error: text("error"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("transcribe_task_user_uid_index").on(table.userId), index("transcribe_task_request_id_index").on(table.requestId)],
);
export type TranscribeTask = typeof transcribeTaskSchema.$inferSelect;
export type NewTranscribeTask = typeof transcribeTaskSchema.$inferInsert;
// ==============project====================
export const projectSchema = sqliteTable(
	"project",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid")
			.unique()
			.notNull()
			.$defaultFn(() => getUUIDString()),
		userId: text("user_uid").notNull(),

		status: integer("status", { mode: "number" }).default(0).notNull(),
		share: integer("share", { mode: "number" }).default(0).notNull(),

		fileName: text("file_name").notNull(),
		fileUrlTemp: text("file_url_temp"),
		fileUrlLocal: text("file_url_local"), // file URL entered by users

		duration: real("duration").default(0).notNull(), // In seconds
		language: text("language").notNull(), // In ISO 639-1 format, eg: en
		speaker: integer("speaker", { mode: "number" }), // null: not set, 0: auto, >1: the number of speakers
		speakerInfo: text("speaker_info"),

		paragraphsUrlPath: text("paragraphs_url_path"),
		isTranscriptEmpty: integer("is_transcript_empty", { mode: "boolean" }).default(false).notNull(),

		creditsSources: text("credits_source"),
		errorInfo: text("error_info"),

		requestId: text("request_id"), //request id from deepgram

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("project_user_id_index").on(table.userId), index("project_request_id_index").on(table.requestId)],
);
export type Project = typeof projectSchema.$inferSelect;
export type NewProject = typeof projectSchema.$inferInsert;

// ============== order ====================
export const orderSchema = sqliteTable(
	"order",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		orderId: text("order_id").unique().notNull(),
		source: text("source").notNull(), // polar, lmsqueezy, cream, etc.
		status: text("status").notNull(),
		billingReason: text("billing_reason"), //在付费成功的order.created中赋值。 type: purchase, subscription_create, subscription_cycle, subscription_update

		subscriptionId: text("subscription_id"),
		checkoutId: text("checkout_id"),
		productId: text("product_id"),

		currency: text("currency"), // usd, etc.
		subtotalAmount: integer("subtotal_amount", { mode: "number" }), // Amount in cents, before discounts and taxes.
		discountAmount: integer("discount_amount", { mode: "number" }), // Discount amount in cents.
		taxAmount: integer("tax_amount", { mode: "number" }), // Sales tax amount in cents.
		netAmount: integer("net_amount", { mode: "number" }), // Amount in cents, after discounts but before taxes.
		totalAmount: integer("total_amount", { mode: "number" }), // Amount in cents, after discounts and taxes.
		refundedAmount: integer("refunded_amount", { mode: "number" }), // Amount refunded in cents.
		refundedTaxAmount: integer("refunded_tax_amount", { mode: "number" }), // Sales tax refunded in cents.

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => {
		return [index("order_user_uid_index").on(table.userId)];
	},
);
export type Order = typeof orderSchema.$inferSelect;
export type NewOrder = typeof orderSchema.$inferInsert;

// ==============subscription====================
export const subscriptionSchema = sqliteTable(
	"subscription",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		subscriptionId: text("subscription_id").unique().notNull(),
		source: text("source").notNull(), // polar, lmsqueezy, cream, etc.
		status: text("status").notNull(),
		recurringInterval: text("recurring_interval").notNull(), // month, year

		productId: text("product_id").notNull(),
		checkoutId: text("checkout_id"),

		currentPeriodStartAt: integer("current_period_start_at", { mode: "timestamp_ms" }).notNull(), // The start timestamp of the current billing period.
		currentPeriodEndAt: integer("current_period_end_at", { mode: "timestamp_ms" }), // The end timestamp of the current billing period.
		cancelAtPeriodEnd: integer("cancel_at_period_end", { mode: "boolean" }).default(false).notNull(), // Whether the subscription will be canceled at the end of the current period.
		canceledAt: integer("canceled_at", { mode: "timestamp_ms" }),
		startAt: integer("start_at", { mode: "timestamp_ms" }), // The timestamp when the subscription started.
		endAt: integer("end_at", { mode: "timestamp_ms" }), // The timestamp when the subscription will end.
		endedAt: integer("ended_at", { mode: "timestamp_ms" }), // The timestamp when the subscription ended.

		customerCancellationReason: text("customer_cancellation_reason"), // The reason for the customer's cancellation.

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("subscription_user_uid_index").on(table.userId)],
);
export type Subscription = typeof subscriptionSchema.$inferSelect; // return type when queried
export type NewSubscription = typeof subscriptionSchema.$inferInsert; // insert type

// ==============user_credits_history====================
export const userCreditsHistorySchema = sqliteTable(
	"user_credits_history",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		creditsFree: integer("credits_free", { mode: "number" }),
		creditsOneTime: integer("credits_one_time", { mode: "number" }),
		creditsSubscription: integer("credits_subscription", { mode: "number" }),

		remark: text("remark"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("user_credits_history_user_uid_index").on(table.userId)],
);
export type UserCreditsHistory = typeof userCreditsHistorySchema.$inferSelect;
export type NewUserCreditsHistory = typeof userCreditsHistorySchema.$inferInsert;

// ==============blog_category====================
export const blogCategorySchema = sqliteTable("blog_category", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	slug: text("slug").unique().notNull(),
	name: text("name"),
	description: text("description"),
	createdAt: integer("created_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date()),
	updatedAt: integer("updated_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date()),
});
export type BlogCategory = typeof blogCategorySchema.$inferSelect;
export type NewBlogCategory = typeof blogCategorySchema.$inferInsert;

// ==============blog_item====================
export const blogItemSchema = sqliteTable(
	"blog_item",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		slug: text("slug").unique().notNull(),
		categoryId: integer("category_id", { mode: "number" }),
		lang: text("lang").default("en").notNull(),

		status: integer("status", { mode: "number" }).default(0).notNull(), // 0: draft, 1: published

		title: text("title").notNull(),
		metaTitle: text("meta_title"),
		metaDescription: text("meta_description"),
		image: text("image"),
		intro: text("intro").notNull(),
		html: text("html"),
		publishedAt: integer("publish_date", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("blog_item_lang_index").on(table.lang),
		index("blog_item_category_index").on(table.categoryId),
		index("blog_item_status_index").on(table.status),
	],
);
export type BlogItem = typeof blogItemSchema.$inferSelect;
export type NewBlogItem = typeof blogItemSchema.$inferInsert;

// ==============changelog_item====================
export const changelogItemSchema = sqliteTable(
	"changelog_item",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		lang: text("lang").default("en").notNull(),

		majorVersion: integer("major_version", { mode: "number" }),
		minorVersion: integer("minor_version", { mode: "number" }),
		patchVersion: integer("patch_version", { mode: "number" }),

		status: integer("status", { mode: "number" }).default(0).notNull(), // 0: draft, 1: published

		title: text("title").notNull(),
		image: text("image"),
		html: text("html"),
		publishedAt: integer("publish_date", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("changelog_item_lang_index").on(table.lang), index("changelog_item_status_index").on(table.status)],
);
export type ChangelogItem = typeof changelogItemSchema.$inferSelect;
export type NewChangelogItem = typeof changelogItemSchema.$inferInsert;
