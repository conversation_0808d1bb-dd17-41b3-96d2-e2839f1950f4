import { ParagraphItem } from "@/@types/project/paragraphs-type";
import { getCurrentMonthAndDayString } from "@/lib/utils-date";
import { createR2Url } from "./r2.server";
import { ofetch } from "ofetch";
import { getUUIDString } from "@/lib/utils";

// generate paragraphs file url path for r2
export function generateParagraphsFileUrlPath() {
	const fileId = getUUIDString();
	const { yearMonth, yearMonthDay } = getCurrentMonthAndDayString();
	if (process.env.NODE_ENV === "development") {
		return `/dev/transcript/${yearMonth}/${yearMonthDay}-${fileId}-paragraphs.json`;
	}
	return `/transcript/${yearMonth}/${yearMonthDay}-${fileId}-paragraphs.json`;
}
// function generateParagraphsFileUrlPath(projectId: number, projectUid: string, language: string) {
// 	return `${OSS_PROJECT_NAME}/transcript/${projectId}/paragraphs-${language}-${projectUid}.json`;
// }

export async function saveParagraphsToR2(path: string, paragraphs: ParagraphItem[]): Promise<string> {
	const jsonString = JSON.stringify(paragraphs, null, 2);
	const fileBlob = new Blob([jsonString], { type: "application/json" });

	const maxRetries = 3;
	let attempt = 0;
	let lastError: Error | null = null;
	while (attempt < maxRetries) {
		try {
			await saveFileToR2(path, fileBlob, "application/json", "paragraphs.json");
			return path;
		} catch (error: any) {
			console.log(`Attempt ${attempt + 1} failed:`, error.message);
			lastError = error as Error;
			attempt++;
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}
	}
	throw lastError!;
}

export async function saveFileToR2(path: string, fileBlob: Blob, contentType: string | null, fileName: string): Promise<string> {
	const file = new File([fileBlob], fileName);

	const signedUrl = await createR2Url(path, contentType);
	await ofetch(signedUrl, {
		method: "PUT",
		body: file,
	});

	return path;
}
