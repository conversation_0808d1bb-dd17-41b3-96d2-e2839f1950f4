import { headers } from "next/headers";

const banIpCountry = [
	"T1", // Tor network
	"AF", // Afghanistan 阿富汗
	"BD", // Bangladesh 孟加拉国
	"CD", // Congo 民主刚果
	"CG", // Congo 刚果
	"GH", // Ghana 加纳
	"ID", // Indonesia 印度尼西亚
	"IN", // India 印度
	"IR", // Iran 伊朗
	"IQ", // Iraq 伊拉克
	"KH", // Cambodia 柬埔寨
	"LA", // Laos" 老挝
	"LY", // Libya 利比亚
	"MM", // Myanmar 缅甸
	"NP", // Nepal 尼泊尔
	"NE", // Niger 尼日尔
	"NG", // Nigeria 尼日利亚
	"PK", // Pakistan 巴基斯坦
	"PH", // Philippines 菲律宾
	"PS", // Palestine 巴勒斯坦
	"RU", // Russia 俄罗斯
	"SD", // Sudan 苏丹
	"SS", // South Sudan 南苏丹
	"LK", // Sri Lanka 斯里兰卡
	"SY", // Syria 叙利亚
	"TZ", // Tanzania 坦桑尼亚
	"VN", // Vietnam 越南
	"YE", // Yemen 也门
];

export async function getCfIpCountry(): Promise<string | null> {
	const headersList = await headers();
	return headersList.get("cf-ipcountry");
}

export async function checkCfIpCountry(cfIpCountryCode?: string | null): Promise<boolean | null> {
	let countryCode = cfIpCountryCode;
	if (!countryCode) {
		countryCode = await getCfIpCountry();
	}
	if (countryCode) {
		return banIpCountry.some((country) => country.toUpperCase() === countryCode.toUpperCase());
	}
	return null;
}
