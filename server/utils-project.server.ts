import { getKVKeyProjectHead } from "@/lib/utils";
import { DURATION_1_DAY } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { projectSchema, userSchema } from "./db/schema.server";
import { eq } from "drizzle-orm";
import { getValue, setValue, deleteValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { z } from "zod";

// export interface ProjectHeadServer {
// 	id: number;
// 	uid: string;
// 	userId: number;
// 	status: number;
// 	share: number;
// 	fileName: string;
// 	fileUrlTemp?: string | null;
// 	fileUrlLocal?: string | null;
// 	duration: number;
// 	language: string;
// 	speakerInfo: string | null;
// 	paragraphsUrlPath?: string | null;
// 	isTranscriptEmpty: boolean;
// 	createdAt: Date;
// }

export const projectHeadServerSchema = z.object({
	id: z.number(),
	uid: z.string().nonempty(),
	userId: z.string(),
	userName: z.string().optional(),
	status: z.number(),
	share: z.number(),
	fileName: z.string().nonempty(),
	fileUrlTemp: z.string().nullable(),
	fileUrlLocal: z.string().nullable(),
	duration: z.number(),
	language: z.string().nonempty(),
	speakerInfo: z.string().nullable(),
	paragraphsUrlPath: z.string().nullable(),
	isTranscriptEmpty: z.boolean(),
	createdAt: z.date(),
});
export type ProjectHeadServer = z.infer<typeof projectHeadServerSchema>;

export async function getProjectHeadRealtime(projectUid: string, userId?: string | null): Promise<ProjectHeadServer | null> {
	let projectHead: ProjectHeadServer | null = null;
	const db = getDB();
	const projectHeads: ProjectHeadServer[] = await db
		.select({
			id: projectSchema.id,
			uid: projectSchema.uid,
			userId: projectSchema.userId,
			status: projectSchema.status,
			share: projectSchema.share,
			fileName: projectSchema.fileName,
			fileUrlTemp: projectSchema.fileUrlTemp,
			fileUrlLocal: projectSchema.fileUrlLocal,
			duration: projectSchema.duration,
			language: projectSchema.language,
			speaker: projectSchema.speaker,
			speakerInfo: projectSchema.speakerInfo,
			paragraphsUrlPath: projectSchema.paragraphsUrlPath,
			isTranscriptEmpty: projectSchema.isTranscriptEmpty,
			createdAt: projectSchema.createdAt,
		})
		.from(projectSchema)
		.where(eq(projectSchema.uid, projectUid));
	// console.log("projectHeads from db:", projectHeads);
	if (projectHeads.length === 0) {
		return null;
	}
	projectHead = projectHeads[0];

	if (userId && projectHead.userId !== userId) {
		return null;
	}

	return projectHead;
}

// Get project head from database or kv
export async function getProjectHead(projectUid: string, userId?: string | null): Promise<ProjectHeadServer | null> {
	let projectHead: ProjectHeadServer | null = null;

	//先从kv中获取
	let cacheKey = getKVKeyProjectHead(projectUid);
	// console.log("cacheUserKey:", cacheKey);
	const kvData = (await getValue(cacheKey)) as any;
	if (kvData) {
		try {
			projectHead = kvData;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog categories data from kv error:", error);
		}
	} else {
		const db = getDB();
		const projectHeads: ProjectHeadServer[] = await db
			.select({
				id: projectSchema.id,
				uid: projectSchema.uid,
				userId: projectSchema.userId,
				userName: userSchema.name,
				status: projectSchema.status,
				share: projectSchema.share,
				fileName: projectSchema.fileName,
				fileUrlTemp: projectSchema.fileUrlTemp,
				fileUrlLocal: projectSchema.fileUrlLocal,
				duration: projectSchema.duration,
				language: projectSchema.language,
				speaker: projectSchema.speaker,
				speakerInfo: projectSchema.speakerInfo,
				paragraphsUrlPath: projectSchema.paragraphsUrlPath,
				isTranscriptEmpty: projectSchema.isTranscriptEmpty,
				createdAt: projectSchema.createdAt,
			})
			.from(projectSchema)
			.innerJoin(userSchema, eq(projectSchema.userId, userSchema.id))
			.where(eq(projectSchema.uid, projectUid));
		// console.log("projectHeads from db:", projectHeads);

		if (projectHeads.length > 0) {
			projectHead = projectHeads[0];
			await setValue(cacheKey, superjson.stringify(projectHead), DURATION_1_DAY);
		}
	}

	if (!projectHead) return null;
	if (userId && projectHead.userId !== userId) {
		return null;
	}

	return projectHead;
}

// Delete project head from kv
export async function deleteProjectHeadFromKV(projectUid: string) {
	await deleteValue(getKVKeyProjectHead(projectUid));
}
