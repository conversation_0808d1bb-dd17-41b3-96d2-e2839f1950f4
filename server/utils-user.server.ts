import { getKVKeyUser } from "@/lib/utils";
import { DURATION_1_DAY } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { NewUserCreditsHistory, User, userCreditsHistorySchema, userSchema } from "./db/schema.server";
import { eq } from "drizzle-orm";
import { deleteValue, getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { addMonths, endOfMonth } from "date-fns";
import { MembershipID, membershipMapping, MembershipPeriodNone, MembershipPeriodYear } from "@/@types/membership-type";

export async function getUser(userUid: string | null | undefined): Promise<User | null> {
	if (!userUid) return null;

	//先从kv中获取
	let cacheUserKey = getKVKeyUser(userUid);
	// console.log("cacheUserKey:", cacheUserKey);
	const kvData = (await getValue(cacheUserKey)) as any;
	let user: User | null = null;

	if (kvData) {
		try {
			user = superjson.deserialize(kvData) as User;
			return user;
		} catch (error) {
			console.error("[getUser] parse data from redis error:", error);
		}
	}

	//再从db中获取
	const db = getDB();
	const users: User[] = await db.select().from(userSchema).where(eq(userSchema.id, userUid));
	if (users.length === 0) return null;
	user = users[0];

	await setValue(cacheUserKey, superjson.stringify(user), DURATION_1_DAY);
	return user;
}

export async function getUserRealtime(userUid?: string | null | undefined): Promise<User | null> {
	if (!userUid) return null;

	const db = getDB();
	const users: User[] = await db.select().from(userSchema).where(eq(userSchema.id, userUid));
	if (users.length === 0) {
		return null;
	}

	return users[0];
}

// reset user subscription membership to free
export async function resetUserMembershipWithSubscription(userId: string | null | undefined) {
	if (!userId) return;

	const db = getDB();
	await db
		.update(userSchema)
		.set({
			membershipId: MembershipID.Free,
			membershipFormatted: membershipMapping[MembershipID.Free].name,

			creditSubscription: 0,

			subscriptionPeriod: MembershipPeriodNone.value,
			subscriptionId: null,
			subscriptionInvoiceEndsAt: null,
			subscriptionExpireAt: null,
			// updatedAt: new Date(),
		})
		.where(eq(userSchema.id, userId));

	await deleteValue(getKVKeyUser(userId));
}

export async function resetUserSubscriptionForYearly(user: User): Promise<User> {
	// the user does not have subscription, return directly
	if (!user.subscriptionId) return user;

	// the user subscription is free or not yearly, return directly
	if (user.membershipId === MembershipID.Free || user.subscriptionPeriod !== MembershipPeriodYear.value) return user;

	// the user subscription current period ends at is in the future(not expired), return directly
	const currentDate = new Date();
	if (!user.creditSubscriptionEndsAt || user.creditSubscriptionEndsAt > currentDate) return user;

	// renew for yearly
	console.log("----refreshUser isRenewForYear for subscription membership");
	// 1. Get next renew date
	let nextRenewDate = addMonths(user.creditSubscriptionEndsAt, 1); // increment month by 1
	// Keep adding months until nextRenewDate is in the future
	while (nextRenewDate < currentDate) {
		nextRenewDate = addMonths(nextRenewDate, 1);
	}

	// 2. Get new credits
	const credits = membershipMapping[user.membershipId as MembershipID].credits;

	// 3. Update tokens to db for subscription yearly user
	const db = getDB();
	const updatedUserAll: User[] = await db
		.update(userSchema)
		.set({
			creditSubscription: credits,
			creditSubscriptionEndsAt: nextRenewDate,
			updatedAt: currentDate,
		})
		.where(eq(userSchema.id, user.id))
		.returning();
	const updatedUser = updatedUserAll[0];

	// 4. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		creditsSubscription: credits,
		remark: `Monthly creditSubscription reset for yearly subscriptions. SubscriptionId: ${user.subscriptionId}`,
	};
	await db.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}

export async function resetUserFreeCreditsMonthly(user: User): Promise<User> {
	// the user has subscription, return directly
	if (user.subscriptionId) return user;

	// the user is not free, return directly
	if (user.membershipId !== MembershipID.Free) return user;

	// the user free credits is not expired(until the end of current month), return directly
	const currentDate = new Date();
	if (user.creditFreeEndsAt && user.creditFreeEndsAt > currentDate) return user;

	// reset free credits monthly
	// 1. Get new credits
	const freeCredits = membershipMapping[user.membershipId as MembershipID].credits;

	// 2. Update tokens to db for free user
	const db = getDB();
	const updatedUserAll: User[] = await db
		.update(userSchema)
		.set({
			creditFree: freeCredits,
			creditFreeEndsAt: endOfMonth(currentDate),
			updatedAt: currentDate,
		})
		.where(eq(userSchema.id, user.id))
		.returning();
	const updatedUser = updatedUserAll[0];

	// 3. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: user.id,
		creditsFree: freeCredits,
		remark: "creditFree reset for free user at the end of current month.",
	};
	await db.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}

export async function resetUserSubscriptionResume(userId: string) {
	const db = getDB();
	await db
		.update(userSchema)
		.set({
			subscriptionExpireAt: null,
			// updatedAt: new Date(),
		})
		.where(eq(userSchema.id, userId));

	await deleteValue(getKVKeyUser(userId));
}

// reset user onetime credits after expired
export async function resetUserOnetimeCredits(user: User): Promise<User> {
	if (!user.creditOneTimeEndsAt) return user;
	const currentDate = new Date();
	if (user.creditOneTimeEndsAt > currentDate) return user;

	// 2. Update tokens to db for free user
	// 3. Insert credits change record to db: user_credits_history
	const db = getDB();
	const updatedUser: User = await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values({
			userId: user.id,
			creditsOneTime: -user.creditOneTime,
			remark: `creditOneTime expired: ${user.creditOneTimeEndsAt}`,
		} as NewUserCreditsHistory);
		const [updatedUser]: User[] = await tx
			.update(userSchema)
			.set({
				membershipId: MembershipID.Free,
				membershipFormatted: membershipMapping[MembershipID.Free].name,
				creditOneTime: 0,
				creditOneTimeEndsAt: null,
				// updatedAt: currentDate,
			})
			.where(eq(userSchema.id, user.id))
			.returning();
		return updatedUser;
	});

	await deleteValue(getKVKeyUser(user.id));

	return updatedUser;
}
