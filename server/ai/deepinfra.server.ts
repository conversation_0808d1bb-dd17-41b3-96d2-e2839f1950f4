import fs from "fs";
import path from "path";
import { pipeline } from "stream/promises";
import { createWriteStream } from "fs";
import { tmpdir } from "os";
import OpenAI from "openai";
import { combineWordsToSentences } from "@/lib/utils-transcript";
import { Readable } from "stream";
import { randomBytes } from "crypto";

// Initialize OpenAI client with DeepInfra configuration
const openai = new OpenAI({
	baseURL: "https://api.deepinfra.com/v1/openai",
	apiKey: process.env.DEEPINFRA_API_KEY,
});

/**
 * Download file from URL to a temporary location using streams
 * @param fileUrl - URL of the file to download
 * @returns Promise<string> - Path to the downloaded temporary file
 */
async function downloadFileToTemp(fileUrl: string): Promise<string> {
	const response = await fetch(fileUrl, {
		headers: {
			"Cache-Control": "no-cache",
		},
	});
	if (!response.ok) {
		throw new Error(`Failed to fetch audio file: ${response.status} ${response.statusText}`);
	}

	if (!response.body) {
		throw new Error("Response body is null");
	}

	// Create temporary file path
	const randomSuffix = randomBytes(8).toString("hex");
	const urlPath = new URL(fileUrl).pathname;
	const extension = path.extname(urlPath);
	const tempFileName = `download_${Date.now()}_${randomSuffix}${extension}`;
	const tempFilePath = path.join(tmpdir(), tempFileName);

	// Stream the response to a temporary file
	const fileStream = createWriteStream(tempFilePath);

	// 将 Response body 转换为 Node.js readable stream
	const readableNodeStream = Readable.from(response.body as unknown as AsyncIterable<Uint8Array>);

	// 使用 pipeline 处理流
	await pipeline(readableNodeStream, fileStream);

	return tempFilePath;
}

/**
 * Clean up temporary file
 * @param filePath - Path to the temporary file to delete
 */
async function cleanupTempFile(filePath: string): Promise<void> {
	try {
		await fs.promises.unlink(filePath);
	} catch (error) {
		console.warn(`Failed to cleanup temporary file ${filePath}:`, error);
	}
}

/**
 * Transcribe audio from a URL using OpenAI Whisper via DeepInfra
 * @param fileUrl - URL of the audio file to transcribe
 * @param languageCode - Language code for transcription (e.g., 'en', 'zh', 'es')
 * @returns Promise<string> - The transcribed text
 */
export async function transcribeFromDeepinfra(
	fileUrl: string, // eg. https://example.com/file.mp3
	languageCode: string,
): Promise<SentenceData[]> {
	let tempFilePath: string | null = null;

	try {
		// Check if it's a URL or local file path
		const isUrl = fileUrl.startsWith("http://") || fileUrl.startsWith("https://");

		let audioFilePath: string;

		if (isUrl) {
			// Download file to temporary location using streams
			if (process.env.NODE_ENV === "development") {
				console.log(`Downloading audio file from URL: ${fileUrl}`);
			}
			tempFilePath = await downloadFileToTemp(fileUrl);
			audioFilePath = tempFilePath;
		} else {
			// Use local file path directly
			audioFilePath = fileUrl;

			// Verify file exists
			if (!fs.existsSync(audioFilePath)) {
				throw new Error(`Local audio file not found: ${audioFilePath}`);
			}
		}
		if (process.env.NODE_ENV === "development") {
			console.log(`Transcribing audio file: ${audioFilePath}`);
		}

		// Create a read stream for the audio file (this doesn't load the entire file into memory)
		const audioStream = fs.createReadStream(audioFilePath);

		// Perform transcription using OpenAI Whisper
		const { duration, words } = await openai.audio.transcriptions.create({
			file: audioStream,
			model: "openai/whisper-large-v3-turbo",
			language: languageCode,
			response_format: "verbose_json",
			timestamp_granularities: ["word"],
		});

		const sentences: SentenceData[] = combineWordsToSentences(words);
		return sentences;
	} catch (error: any) {
		console.error("Transcription error:", error);

		// Handle specific error types
		if (error.message?.includes("fetch") || error.message?.includes("download")) {
			throw new Error(`Failed to download audio file from URL: ${error.message}`);
		}

		if (error.message?.includes("not found")) {
			throw new Error(`Audio file not found: ${error.message}`);
		}

		if (error.code === "ENOENT") {
			throw new Error(`Audio file not accessible: ${fileUrl}`);
		}

		if (error.status === 401) {
			throw new Error("Authentication failed: Invalid DeepInfra API key");
		}

		if (error.status === 429) {
			throw new Error("Rate limit exceeded: Too many requests to DeepInfra API");
		}

		// Generic error
		throw new Error(`Transcription error: ${error.message || "Unknown error"}`);
	} finally {
		// Clean up temporary file if it was created
		if (tempFilePath) {
			await cleanupTempFile(tempFilePath);
		}
	}
}
