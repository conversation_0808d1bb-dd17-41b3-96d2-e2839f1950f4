import { getUUIDString } from "@/lib/utils";
import { CALLBACK_URL_TRANSCRIBE_WEBHOOK } from "@/lib/constants";
import { getLanguageName } from "@/lib/languages";
import { NewProject, projectSchema, User } from "../db/schema.server";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { ofetch } from "ofetch";
import { getDB } from "../db/db-client.server";
import { ProjectStatus } from "@/@types/project/project-type";

export async function CreateTranscribeTask(
	fileOptions: {
		source: string;
		lang: string;
		speaker: boolean;
		fileName: string;
		fileUrl: string;
		fileDuration: number;
	},
	user: User,
	cfIpCountryCode: string | null,
): Promise<{ status: number; message: string; newProjectHead?: any }> {
	const userId = user.id;

	//1. 检查用户是否有足够的credits
	const needCredits = Math.ceil(fileOptions.fileDuration / 60);
	const { creditConsumes, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});

	//2. 创建转录任务
	//track
	// mixpanelTrackEvent("transcribe-start", userId, {
	// 	mp_country_code: cfIpCountryCode,
	// 	Language: getLanguageName(fileOptions.lang),
	// 	Source: fileOptions.source,
	// });
	const projectUid = getUUIDString();
	const webhookUrl = `${CALLBACK_URL_TRANSCRIBE_WEBHOOK}/api/webhook/deepgram`;
	let transcribeUrl = `https://api.deepgram.com/v1/listen?smart_format=true&language=${fileOptions.lang}&model=nova-2&callback=${webhookUrl}`;
	// if (fileOptions.speaker === 0) {
	// 	transcribeUrl = `${transcribeUrl}&diarize=true`;
	// }
	// console.log("transcribeUrl:", transcribeUrl);
	const { request_id } = await ofetch(transcribeUrl, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Token ${process.env.DEEPGRAM_KEY}`,
		},
		body: {
			url: fileOptions.fileUrl,
		},
	});

	// 3. create new project and save to db
	const db = getDB();
	const insertData: NewProject = {
		uid: projectUid,
		userId: user.id,
		status: ProjectStatus.TranscriptionInProgress,
		fileName: fileOptions.fileName,
		fileUrlLocal: fileOptions.source === "url" ? fileOptions.fileUrl : null,
		fileUrlTemp: fileOptions.fileUrl,
		duration: fileOptions.fileDuration,
		language: fileOptions.lang,
		speaker: fileOptions.speaker ? 0 : null,
		creditsSources: JSON.stringify(creditConsumes),
		requestId: request_id,
	};
	if (fileOptions.source === "url") {
		insertData.fileUrlLocal = fileOptions.fileUrl;
	}
	await db.insert(projectSchema).values(insertData);

	// 4.更新用户credits
	await updateUserCredit(userId, creditConsumes, {
		remark: `Transcribe ${fileOptions.fileName}`,
	});

	// return new project head
	return {
		status: 200,
		message: "",
		newProjectHead: {
			projectUid: projectUid,
			fileName: fileOptions.fileName,
			duration: fileOptions.fileDuration,
			language: fileOptions.lang,
			status: ProjectStatus.TranscriptionInProgress,
			createdAt: new Date().getTime(),
		},
	};
}
