import { getLanguageSplitSpace } from "@/lib/languages";
import { combineWordsToSentences } from "@/lib/utils-transcript";
import { ofetch } from "ofetch";

/**
 * Transcribe audio from a URL via Fireworks
 * @param fileUrl - URL of the audio file to transcribe
 * @param languageCode - Language code for transcription (e.g., 'en', 'zh', 'es')
 */
export async function transcribeFromFireworks(fileUrl: string, languageCode: string): Promise<SentenceData[]> {
	try {
		const formData = new FormData();
		formData.append("file", fileUrl);
		formData.append("model", "whisper-v3-turbo");
		formData.append("alignment_model", languageCode === "en" ? "tdnn_ffn" : "mms_fa");
		formData.append("language", languageCode);
		formData.append("response_format", "verbose_json");
		formData.append("timestamp_granularities", "word");
		formData.append("diarize", "true");
		const { duration, words } = await ofetch("https://audio-turbo.us-virginia-1.direct.fireworks.ai/v1/audio/transcriptions", {
			method: "POST",
			headers: {
				Authorization: `Bearer ${process.env.FIREWORKS_API_KEY!}`,
			},
			body: formData,
		});
		// console.log("words:", JSON.stringify(words));

		const sentences: SentenceData[] = combineWordsToSentences(words, getLanguageSplitSpace(languageCode));
		return sentences;
	} catch (error: any) {
		console.error("Transcription error:", error);

		if (error.status === 429) {
			throw new Error("Rate limit exceeded: Too many requests");
		}

		// Generic error
		throw new Error(`Transcription error: ${error.message || "Unknown error"}`);
	}
}
