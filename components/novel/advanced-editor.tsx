"use client";

import React, { useState } from "react";
import {
	<PERSON><PERSON><PERSON>,
	Editor<PERSON><PERSON><PERSON>,
	EditorCommand<PERSON>tem,
	EditorC<PERSON>mandEmpty,
	Editor<PERSON><PERSON><PERSON>,
	J<PERSON><PERSON><PERSON><PERSON>,
	EditorCommandList,
	EditorBubble,
	EditorInstance,
	ImageResizer,
	handleCommandNavigation,
	handleImageDrop,
	handleImagePaste,
} from "novel";
import { NodeSelector } from "./selectors/node-selector";
import { LinkSelector } from "./selectors/link-selector";
import { ColorSelector } from "./selectors/color-selector";
import { TextButtons } from "./selectors/text-buttons";
import { Separator } from "@/components/ui/separator";
import { defaultExtensions } from "./extensions";
import { slashCommand, suggestionItems } from "./slash-command";
import { uploadFn } from "./image-upload";

const extensions = [...defaultExtensions, slashCommand];

interface EditorProp {
	initialHtmlJson?: JSONContent;
	// initialHtml?: string;
	onChange: (value: string) => void;
}
const Editor = ({ initialHtmlJson, onChange }: EditorProp) => {
	// const [jsonContent, setJsonContent] = useState<JSONContent | undefined>(
	// 	initialHtml ? generateJSON(initialHtml, [Bold, BulletList, Document, Heading, TipTapLink, Paragraph, ListItem, Text, TextStyle, Color]) : undefined,
	// );
	const [jsonContent, setJsonContent] = useState<JSONContent | undefined>(initialHtmlJson);
	const [openNode, setOpenNode] = useState(false);
	const [openColor, setOpenColor] = useState(false);
	const [openLink, setOpenLink] = useState(false);

	// const debouncedUpdates = useDebounceCallback(async (editor: EditorInstance) => {
	// 	const json = editor.getJSON();
	// 	setJsonContent(json);
	// 	onChange(editor.getHTML());
	// 	// console.log(JSON.stringify(json));
	// }, 1000);
	const debouncedUpdates = async (editor: EditorInstance) => {
		const json = editor.getJSON();
		setJsonContent(json);
		onChange(editor.getHTML());
		// console.log(JSON.stringify(json));
		// console.log(editor.getHTML());
	};

	// useEffect(() => {
	// 	console.log("initialHtml:", initialHtml);
	// 	if (initialHtml) {
	// 		const json = generateJSON(initialHtml, [Bold, BulletList, Document, Heading, TipTapLink, Paragraph, ListItem, Text, TextStyle, Color]);
	// 		console.log("json:", json);
	// 		setJsonContent(json);
	// 	}
	// }, [initialHtml]);

	// useEffect(() => {
	// 	console.log("jsonContent:", jsonContent);
	// }, [jsonContent]);

	return (
		<EditorRoot>
			<EditorContent
				className=""
				initialContent={jsonContent}
				// {...(jsonContent && { initialContent: jsonContent })}
				extensions={extensions}
				editorProps={{
					handleDOMEvents: {
						keydown: (_view, event) => handleCommandNavigation(event),
					},
					handlePaste: (view, event) => handleImagePaste(view, event, uploadFn),
					handleDrop: (view, event, _slice, moved) => handleImageDrop(view, event, moved, uploadFn),
					attributes: {
						class: `prose dark:prose-invert prose-headings:font-title font-default focus:outline-hidden max-w-full`,
					},
				}}
				// immediatelyRender={true}
				onUpdate={({ editor }) => debouncedUpdates(editor)}
				slotAfter={<ImageResizer />}
			>
				<EditorCommand className="z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-muted bg-background px-1 py-2 shadow-md transition-all">
					<EditorCommandEmpty className="px-2 text-muted-foreground">No results</EditorCommandEmpty>
					<EditorCommandList>
						{suggestionItems.map((item) => (
							<EditorCommandItem
								value={item.title}
								onCommand={(val) => item.command?.(val)}
								className={`flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-accent aria-selected:bg-accent`}
								key={item.title}
							>
								<div className="flex h-10 w-10 items-center justify-center rounded-md border border-muted bg-background">{item.icon}</div>
								<div>
									<p className="font-medium">{item.title}</p>
									<p className="text-xs text-muted-foreground">{item.description}</p>
								</div>
							</EditorCommandItem>
						))}
					</EditorCommandList>
				</EditorCommand>

				<EditorBubble
					tippyOptions={{
						placement: "top",
					}}
					className="flex w-fit max-w-[90vw] overflow-hidden rounded-md border border-muted bg-background shadow-xl"
				>
					<Separator orientation="vertical" />
					<NodeSelector open={openNode} onOpenChange={setOpenNode} />
					<Separator orientation="vertical" />

					<LinkSelector open={openLink} onOpenChange={setOpenLink} />
					<Separator orientation="vertical" />
					<TextButtons />
					<Separator orientation="vertical" />
					<ColorSelector open={openColor} onOpenChange={setOpenColor} />
				</EditorBubble>
			</EditorContent>
		</EditorRoot>
	);
};

export default Editor;
