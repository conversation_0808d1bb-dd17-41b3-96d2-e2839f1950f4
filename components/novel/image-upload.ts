import { handleError } from "@/@types/error";
import { createImageUpload } from "novel";
import { ofetch } from "ofetch";
import { toast } from "sonner";

const onUpload = async (file: File) => {
	const uploadPromise = new Promise<string>(async (resolve, reject) => {
		try {
			// Get file extension
			const fileExtension = file.name.split(".").pop();

			// First request to get upload URL
			const { status, message, url, method, file_url } = await ofetch("/api/admin/upload-file", {
				method: "POST",
				body: {
					fileExtension: `.${fileExtension}`,
					contentType: file.type,
				},
			});
			handleError(status, message);

			// Second request to upload file
			await ofetch(url!, {
				method: method,
				headers: {
					accept: "application/json",
				},
				body: file,
			});

			// Preload the image
			const image = new Image();
			image.src = file_url;

			image.onload = () => {
				resolve(file_url);
			};

			image.onerror = () => {
				reject(new Error("Failed to load uploaded image."));
			};
		} catch (error) {
			if (error instanceof Error) {
				reject(error);
			} else {
				reject(new Error("Failed to upload file."));
			}
		}
	});

	toast.promise(uploadPromise, {
		loading: "Uploading image...",
		success: "Image uploaded successfully.",
		error: (error: Error) => error.message,
	});

	return uploadPromise;
};

export const uploadFn = createImageUpload({
	onUpload,
	validateFn: (file) => {
		if (!file.type.includes("image/")) {
			toast.error("File type not supported.");
			return false;
		} else if (file.size / 1024 / 1024 > 2) {
			toast.error("File size too big (max 2MB).");
			return false;
		}
		return true;
	},
});
