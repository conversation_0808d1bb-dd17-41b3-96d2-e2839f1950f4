import { LogoDE, LogoES, LogoFR, LogoJP, LogoUS } from "@/components/icons";
import { AnimatedCard } from "@/components/ui/aceternity-ui/feature-block-animated-card";

export function FeatureBlockTranslation() {
	return (
		<AnimatedCard
			className="p-0"
			// title="Damn good card"
			// description="A card that showcases a set of tools that you use to create your product."
			icons={[
				{
					icon: <LogoES className="h-4 w-4" />,
					size: "sm",
				},
				{
					icon: <LogoFR className="h-6 w-6 dark:text-white" />,
					size: "md",
				},
				{
					icon: <LogoUS className="h-8 w-8 dark:text-white" />,
					size: "lg",
				},
				{
					icon: <LogoDE className="h-6 w-6" />,
					size: "md",
				},
				{
					icon: <LogoJP className="h-4 w-4" />,
					size: "sm",
				},
			]}
		/>
	);
}

export function FeatureBlockSpeaker() {
	return (
		<AnimatedCard
			className="p-0"
			// title="Damn good card"
			// description="A card that showcases a set of tools that you use to create your product."
			icons={[
				{
					icon: (
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" className="text-green-500">
							<circle cx="12" cy="6" r="4" fill="currentColor" />
							<ellipse cx="12" cy="17" fill="currentColor" opacity=".5" rx="7" ry="4" />
						</svg>
					),
					size: "md",
				},
				{
					icon: (
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" className="text-blue-600">
							<path fill="currentColor" d="M15.5 7.5a3.5 3.5 0 1 1-7 0a3.5 3.5 0 0 1 7 0" />
							<path
								fill="currentColor"
								d="M19.5 7.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-15 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 0 0-5 0"
								opacity=".4"
							/>
							<path fill="currentColor" d="M18 16.5c0 1.933-2.686 3.5-6 3.5s-6-1.567-6-3.5S8.686 13 12 13s6 1.567 6 3.5" />
							<path
								fill="currentColor"
								d="M22 16.5c0 1.38-1.79 2.5-4 2.5s-4-1.12-4-2.5s1.79-2.5 4-2.5s4 1.12 4 2.5m-20 0C2 17.88 3.79 19 6 19s4-1.12 4-2.5S8.21 14 6 14s-4 1.12-4 2.5"
								opacity=".4"
							/>
						</svg>
					),
					size: "lg",
				},
				{
					icon: (
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" className="text-green-500">
							<circle cx="12" cy="6" r="4" fill="currentColor" />
							<ellipse cx="12" cy="17" fill="currentColor" opacity=".5" rx="7" ry="4" />
						</svg>
					),
					size: "md",
				},
			]}
		/>
	);
}
