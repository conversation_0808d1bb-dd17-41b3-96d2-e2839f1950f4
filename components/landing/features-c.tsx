"use client";

import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";

type Feature = {
	title: string;
	description: string;
	beforeSrc: string;
	afterSrc: string;
};

export default function FeaturesComparisonComponent({ ctaText, ctaUrl, features }: { ctaText: string; ctaUrl: string; features: Feature[] }) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-20">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div className={cn("block w-full", index % 2 === 0 ? "" : "md:order-last")}>
							{/* <div className="w-full overflow-hidden rounded-xl">
								<TwoUpClient beforeSrc={feature.beforeSrc} afterSrc={feature.afterSrc} />
							</div> */}
							<Comparison className="aspect-3/2 min-w-[280px] rounded-lg md:min-w-[318px]">
								<ComparisonItem position="right" className="">
									<div className="relative aspect-3/2">
										<img
											src={feature.beforeSrc}
											alt=""
											className="h-full w-full rounded-lg object-cover"
											onContextMenu={(e) => e.preventDefault()}
											onDragStart={(e) => e.preventDefault()}
										/>
									</div>
								</ComparisonItem>
								<ComparisonItem position="left" className="">
									<div className="relative aspect-3/2">
										<img
											src={feature.afterSrc}
											alt=""
											className="h-full w-full rounded-lg object-cover"
											onContextMenu={(e) => e.preventDefault()}
											onDragStart={(e) => e.preventDefault()}
										/>
									</div>
								</ComparisonItem>
								<ComparisonHandle />
							</Comparison>
						</div>
						<div className="flex h-full flex-col items-start justify-between gap-4">
							<div className="flex flex-col gap-2">
								<h2 className="text-balance text-[32px] font-semibold">{feature.title}</h2>
								<p className="text-base text-muted-foreground">{feature.description}</p>
							</div>
							<NoPrefetchLink
								href="/photo-to-pixel-art"
								className={cn(buttonVariants({ size: "lg" }), "bg-teal-500 after:content-(--content) hover:bg-teal-600")}
								style={{ "--content": `'Convert Photo to Pixel Art'` } as React.CSSProperties}
							></NoPrefetchLink>
						</div>
					</div>
				</div>
			))}
		</>
	);
}
