"use client";

import React, { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export function HowToUse({
	title,
	description,
	steps,
	...props
}: {
	title?: string;
	description?: string;
	steps: {
		title: string;
		description?: string;
		url?: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className="flex flex-col items-center gap-12 bg-zinc-900 py-32">
			<div className="container px-6 text-center">
				<h2 className="text-3xl font-semibold text-pretty text-white">{title}</h2>
				{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
			</div>

			<div className={cn("container grid grid-cols-1 gap-4 px-6 sm:grid-cols-2 sm:gap-6", steps.length === 4 ? "lg:grid-cols-2" : "lg:grid-cols-3")}>
				{steps.map((step, index) => (
					<div key={index} className="flex max-w-full flex-col items-center justify-between gap-6 rounded-xl border border-zinc-700 bg-zinc-800 p-6">
						<div className="space-y-4">
							<h3 className="text-xl font-medium text-zinc-100">{step.title}</h3>
							<p className="text-muted text-sm">{step.description}</p>
						</div>
						{step.url && (
							<div className="w-full">
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							</div>
						)}
						{/* <div className="w-full">
							{step.url ? (
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							) : (
								<p
									className="text-right text-6xl font-extrabold text-neutral-300 after:content-(--content) md:text-7xl"
									style={{ "--content": `"${index + 1}"` } as React.CSSProperties}
								></p>
							)}
						</div> */}
					</div>
				))}
			</div>
		</div>
	);
}
