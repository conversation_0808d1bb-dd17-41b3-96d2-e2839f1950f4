import { type LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

// export function GridSections({
// 	title,
// 	description,
// 	sections,
// }: {
// 	title?: string;
// 	description?: string;
// 	sections: {
// 		title: string;
// 		description: string;
// 		icon: LucideIcon;
// 	}[];
// }) {
// 	return (
// 		<div className="container flex flex-col items-center gap-16 px-6 py-16">
// 			{title && (
// 				<div className="text-center">
// 					<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
// 					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
// 				</div>
// 			)}

// 			<div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-2 sm:gap-10 lg:grid-cols-3">
// 				{sections.map((section, index) => (
// 					<div key={index}>
// 						<section.icon className={cn("h-7 w-7 text-blue-500 transition-transform duration-300")} strokeWidth={1.5} />
// 						<h3 className="mt-6 text-base font-medium md:text-lg">{section.title}</h3>
// 						<p className="text-muted-foreground mt-2">{section.description}</p>
// 					</div>
// 				))}
// 			</div>
// 		</div>
// 	);
// }

export function GridSections({
	title,
	description,
	sections,
}: {
	title?: string;
	description?: string;
	sections: {
		title: string;
		description: string;
		icon?: LucideIcon;
	}[];
}) {
	return (
		<div className="container flex flex-col items-center gap-12 px-6 py-20">
			{title && (
				<div className="text-center">
					<h2 className="text-3xl font-medium text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>
			)}

			<div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-2 md:gap-8 lg:grid-cols-3">
				{sections.map((section, index) => (
					<div key={index} className="flex flex-col items-center gap-4 rounded-xl border bg-neutral-100 p-6 sm:items-start md:p-10">
						{section.icon ? (
							<section.icon className="h-6 w-6 text-blue-500" strokeWidth={2} />
						) : (
							<div className="flex size-10 items-center justify-center rounded-full bg-zinc-100 font-semibold">{index + 1}</div>
						)}
						<h3 className="text-base font-medium md:text-lg">{section.title}</h3>
						<p className="text-muted-foreground -mt-1 text-sm">{section.description}</p>
					</div>
				))}
			</div>
		</div>
	);
}
