import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export function Example({
	title,
	description,
	images,
	className,
	imageClassName,
}: { title?: string; description?: string; images: string[]; imageClassName?: string } & ComponentProps<"div">) {
	return (
		<div className={cn("container flex flex-col items-center gap-16 px-6 py-20", className)}>
			{title && (
				<div className="text-center">
					<h2 className="text-pretty text-[32px] font-semibold">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-muted-foreground">{description}</p>}
				</div>
			)}

			<div className="grid w-full grid-cols-2 gap-3 sm:grid-cols-4">
				{images.map((image, index) => (
					<div key={index} className="">
						<img src={image} alt="" className={cn("h-full w-full rounded-lg object-cover", imageClassName)} loading="lazy" />
					</div>
				))}
			</div>
		</div>
	);
}
