import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ROUTE_PATH_SIGN_IN } from "@/lib/constants";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

export default function FinalCTA({
	ctaText = "Start For Now",
	ctaTextDisplay,
	ctaUrl = ROUTE_PATH_SIGN_IN,
	ctaClassName,
	title = "",
	description,
}: {
	ctaText?: string;
	ctaTextDisplay?: boolean;
	ctaUrl?: string;
	ctaClassName?: string;
	title?: string;
	description?: string;
}) {
	return (
		<div className="bg-muted flex flex-col items-center gap-16 px-6 py-20">
			<div className="text-center">
				<h2 className="text-3xl font-semibold text-pretty">{title}</h2>
				{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
			</div>

			<div className="flex flex-col items-center gap-3">
				{ctaTextDisplay ? (
					<NoPrefetchLink
						href={ctaUrl}
						className={cn(buttonVariants({ size: "lg", variant: "default" }), "h-12 bg-blue-500 px-8 hover:bg-blue-600", ctaClassName)}
					>
						{ctaText}
					</NoPrefetchLink>
				) : (
					<NoPrefetchLink
						href={ctaUrl}
						className={cn(
							buttonVariants({ size: "lg", variant: "default" }),
							"h-12 bg-blue-500 px-8 after:content-(--content) hover:bg-blue-600",
							ctaClassName,
						)}
						style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
					></NoPrefetchLink>
				)}
			</div>
		</div>
	);
}
