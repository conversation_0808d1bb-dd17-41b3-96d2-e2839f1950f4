import { ChevronDownIcon } from "lucide-react";

type Faq = {
	question: string;
	answer: string;
};

export default function FAQsComponent({ title, faqs }: { title?: string; faqs: Faq[] }) {
	return (
		<div id="faq" className="py-20">
			<div className="container flex flex-col items-center justify-center gap-3 px-6 md:gap-12">
				<div className="max-w-4xl text-center">
					<h2 className="text-3xl font-medium text-pretty">{title ?? "Frequently asked questions"}</h2>
				</div>
				{/* <div className="w-full max-w-4xl">
					{faqs.map((faq, index) => (
						<div key={index} className="py-1">
							<div className="py-3 text-start text-base font-medium hover:no-underline">{faq.question}</div>
							<div className="text-sm text-muted-foreground">{faq.answer}</div>
						</div>
					))}
				</div> */}
				<div className="w-full max-w-4xl">
					<div className="space-y-2">
						{faqs.map((faq, index) => (
							<details key={index} className="group rounded-lg border px-4" open={false}>
								<summary className="flex cursor-pointer list-none items-center justify-between py-4 text-[17px] font-medium">
									<h3>{faq.question}</h3>
									<ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200 group-open:rotate-180" />
								</summary>
								<div className="text-muted-foreground grid grid-rows-[0fr] overflow-hidden pb-4 text-sm transition-[grid-template-rows] duration-300 ease-in-out group-open:grid-rows-[1fr]">
									<div className="min-h-0">
										<p>{faq.answer}</p>
									</div>
								</div>
							</details>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
