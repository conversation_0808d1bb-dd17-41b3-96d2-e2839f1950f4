import { cn } from "@/lib/utils";

interface BentoItemProps {
	title: string;
	description: string;
	className?: string;
	children?: React.ReactNode;
}

export const BentoItem: React.FC<BentoItemProps> = ({ title, description, className = "col-span-1", children }) => {
	return (
		<div className={cn("bg-muted overflow-hidden rounded-2xl p-6 md:p-10", className)}>
			<div className="flex h-full flex-col">
				<div className="flex flex-col justify-start">
					<h2 className="mb-4 text-3xl font-medium">{title}</h2>
					<p className="text-muted-foreground font-[380]">{description}</p>
				</div>
				<div className="flex flex-1">{children}</div>
			</div>
		</div>
	);
};

// {features && features.length > 0 && (
// 	<>
// 		{features.map(({ title, description, image, imagePadding = true }, index) => (
// 			<div key={index} className="container flex flex-col items-center gap-16 px-6 py-16">
// 				<div className="flex flex-col items-center justify-center gap-6">
// 					<h2 className="text-center text-4xl font-medium tracking-wide">{title}</h2>
// 					<p className="mx-auto max-w-2xl text-center text-lg leading-relaxed text-black/60">{description}</p>
// 				</div>
// 				<div className={cn("max-w-4xl rounded-2xl shadow-normal-xl transition-all", imagePadding ? "p-6" : "")}>
// 					<img src={image} alt={title} className={cn("relative w-full", imagePadding ? "rounded-xl" : "rounded-2xl")} loading="lazy" />
// 				</div>
// 			</div>
// 		))}
// 	</>
// )}
