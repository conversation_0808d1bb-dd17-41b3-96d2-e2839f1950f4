import React, { memo } from "react";
import remarkMath from "remark-math";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

const NonMemoizedMarkdown = ({ children }: { children: string }) => {
	const preprocessMarkdown = (markdownText: string) => {
		// Replace \[ with $$ and \] with $$ to ensure compatibility
		const processedText = markdownText
			.replace(/\\\[/g, "$$$") // Replace all occurrences of \[ with $$
			.replace(/\\\]/g, "$$$") // Replace all occurrences of \] with $$
			.replace(/\\\(/g, "$$$") // Replace all occurrences of \( with $$
			.replace(/\\\)/g, "$$$"); // Replace all occurrences of \) with $$

		return processedText;
	};

	const components = {
		ul: ({ node, children, ...props }: any) => {
			return (
				<ul className="my-6 ml-6 list-disc [&>li]:mt-2" {...props}>
					{children}
				</ul>
			);
		},
		p({ node, children, ...props }: any) {
			return (
				<p className="[&:not(:first-child)]:mt-6" {...props}>
					{children}
				</p>
			);
		},
		blockquote: ({ node, children, ...props }: any) => {
			return (
				<blockquote className="mt-6 border-l-2 pl-6 italic" {...props}>
					{children}
				</blockquote>
			);
		},
		code({ node, children, ...props }: any) {
			return (
				<code className="bg-muted relative rounded px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold" {...props}>
					{children}
				</code>
			);
		},
		a: ({ node, children, ...props }: any) => {
			return (
				<a className="text-primary font-medium underline underline-offset-4" target="_blank" rel="noreferrer nofollow noopener" {...props}>
					{children}
				</a>
			);
		},
		h1: ({ node, children, ...props }: any) => {
			return (
				<h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight text-balance" {...props}>
					{children}
				</h1>
			);
		},
		h2: ({ node, children, ...props }: any) => {
			return (
				<h2 className="mt-10 scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0" {...props}>
					{children}
				</h2>
			);
		},
		h3: ({ node, children, ...props }: any) => {
			return (
				<h3 className="mt-8 scroll-m-20 text-2xl font-semibold tracking-tight" {...props}>
					{children}
				</h3>
			);
		},
		h4: ({ node, children, ...props }: any) => {
			return (
				<h4 className="mt-6 scroll-m-20 text-xl font-semibold tracking-tight" {...props}>
					{children}
				</h4>
			);
		},
	};

	return (
		<ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} components={components}>
			{preprocessMarkdown(children)}
		</ReactMarkdown>
	);
};

export default memo(NonMemoizedMarkdown, (prevProps, nextProps) => prevProps.children === nextProps.children);
