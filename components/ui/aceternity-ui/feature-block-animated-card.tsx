"use client";

import { forwardRef } from "react";
import { cn } from "@/lib/utils";

export interface AnimatedCardProps {
	className?: string;
	title?: React.ReactNode;
	description?: React.ReactNode;
	icons?: Array<{
		icon: React.ReactNode;
		size?: "sm" | "md" | "lg";
		className?: string;
	}>;
}

const sizeMap = {
	sm: "h-8 w-8",
	md: "h-12 w-12",
	lg: "h-16 w-16",
};

export function AnimatedCard({ className, title, description, icons = [] }: AnimatedCardProps) {
	return (
		<div
			className={cn(
				"group mx-auto w-full max-w-sm rounded-xl border border-[rgba(255,255,255,0.10)] p-6 shadow-[2px_4px_16px_0px_rgba(248,248,248,0.06)_inset] dark:bg-[rgba(40,40,40,0.70)]",
				className,
			)}
		>
			<div
				className={cn(
					"z-40 h-[14rem] rounded-xl md:h-[18rem]",
					"bg-neutral-300 [mask-image:radial-gradient(50%_50%_at_50%_50%,white_0%,transparent_100%)] dark:bg-[rgba(40,40,40,0.70)]",
				)}
			>
				<AnimatedIcons icons={icons} />
			</div>
			{title && <h3 className="py-2 text-lg font-semibold text-gray-800 dark:text-white">{title}</h3>}
			{description && <p className="max-w-sm text-sm font-normal text-neutral-600 dark:text-neutral-400">{description}</p>}
		</div>
	);
}

function AnimatedIcons({ icons }: { icons: AnimatedCardProps["icons"] }) {
	// const scale = [1, 1.1, 1];
	// const transform = ["translateY(0px)", "translateY(-4px)", "translateY(0px)"];

	// const sequence = icons!.map((_, index) => [`.circle-${index + 1}`, { scale, transform }, { duration: 0.8 }]);

	// useEffect(() => {
	// 	animate(sequence, {
	// 		repeat: Infinity,
	// 		repeatDelay: 1,
	// 	});
	// }, []);

	return (
		<div className="relative flex h-full items-center justify-center overflow-hidden p-8">
			<div className="flex flex-shrink-0 flex-row items-center justify-center gap-2">
				{icons!.map((icon, index) => (
					<Container key={index} className={cn(sizeMap[icon.size || "lg"], `circle-${index + 1}`, icon.className)}>
						{icon.icon}
					</Container>
				))}
			</div>
			{/* <AnimatedSparkles /> */}
		</div>
	);
}

const Container = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ className, ...props }, ref) => (
	<div
		ref={ref}
		className={cn(
			`flex items-center justify-center rounded-full bg-[rgba(248,248,248,0.01)] shadow-[0px_0px_8px_0px_rgba(248,248,248,0.25)_inset,0px_32px_24px_-16px_rgba(0,0,0,0.40)]`,
			className,
		)}
		{...props}
	/>
));
Container.displayName = "Container";

// const AnimatedSparkles = () => (
// 	<div className="animate-move absolute top-20 z-40 m-auto h-40 w-px bg-gradient-to-b from-transparent via-cyan-500 to-transparent">
// 		<div className="absolute -left-10 top-1/2 h-32 w-10 -translate-y-1/2">
// 			<Sparkles />
// 		</div>
// 	</div>
// );

// const Sparkles = () => {
// 	const randomMove = () => Math.random() * 2 - 1;
// 	const randomOpacity = () => Math.random();
// 	const random = () => Math.random();

// 	return (
// 		<div className="absolute inset-0">
// 			{[...Array(12)].map((_, i) => (
// 				<motion.span
// 					key={`star-${i}`}
// 					animate={{
// 						top: `calc(${random() * 100}% + ${randomMove()}px)`,
// 						left: `calc(${random() * 100}% + ${randomMove()}px)`,
// 						opacity: randomOpacity(),
// 						scale: [1, 1.2, 0],
// 					}}
// 					transition={{
// 						duration: random() * 2 + 4,
// 						repeat: Infinity,
// 						ease: "linear",
// 					}}
// 					style={{
// 						position: "absolute",
// 						top: `${random() * 100}%`,
// 						left: `${random() * 100}%`,
// 						width: `2px`,
// 						height: `2px`,
// 						borderRadius: "50%",
// 						zIndex: 1,
// 					}}
// 					className="inline-block bg-black dark:bg-white"
// 				/>
// 			))}
// 		</div>
// 	);
// };
