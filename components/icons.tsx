import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export const IconMusicFile = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		// <svg viewBox="-1.5 -1.5 18.00 18.00" xmlns="http://www.w3.org/2000/svg" className={cn("text-inherit", className)}>
		// 	<g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
		// 	<g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
		// 	<g id="SVGRepo_iconCarrier">
		// 		<path
		// 			d="M7 10C7 9.44771 6.55228 9 6 9C5.44772 9 5 9.44771 5 10C5 10.5523 5.44772 11 6 11C6.55228 11 7 10.5523 7 10Z"
		// 			fill="currentColor"
		// 		></path>
		// 		<path
		// 			fill="currentColor"
		// 			fillRule="evenodd"
		// 			clipRule="evenodd"
		// 			d="M1 1.5C1 0.671573 1.67157 0 2.5 0H10.7071L14 3.29289V13.5C14 14.3284 13.3284 15 12.5 15H2.5C1.67157 15 1 14.3284 1 13.5V1.5ZM7.34189 4.02569C7.54606 3.95763 7.77087 4.02786 7.9 4.20003L8.2 4.60003C8.86099 5.48135 9.89835 6.00003 11 6.00003V7.00003C9.88299 7.00003 8.8174 6.58529 8 5.8542V10C8 11.1046 7.10457 12 6 12C4.89543 12 4 11.1046 4 10C4 8.89543 4.89543 8 6 8C6.36429 8 6.70583 8.09739 7 8.26756V4.50003C7 4.28482 7.13772 4.09375 7.34189 4.02569Z"
		// 		></path>
		// 	</g>
		// </svg>
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" className={cn("text-inherit", className)}>
			<path fill="currentColor" d="M7 10a1 1 0 1 0-2 0a1 1 0 0 0 2 0" />
			<path
				fill="currentColor"
				fillRule="evenodd"
				d="M1 1.5A1.5 1.5 0 0 1 2.5 0h8.207L14 3.293V13.5a1.5 1.5 0 0 1-1.5 1.5h-10A1.5 1.5 0 0 1 1 13.5zm6.342 2.526A.5.5 0 0 1 7.9 4.2l.3.4A3.5 3.5 0 0 0 11 6v1a4.5 4.5 0 0 1-3-1.146V10a2 2 0 1 1-1-1.732V4.5a.5.5 0 0 1 .342-.474"
				clipRule="evenodd"
			/>
		</svg>
	);
};

export const LogoGoogle = ({ className }: { className?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" className={className}>
			<path
				fill="#fff"
				d="M44.59 4.21a63.28 63.28 0 0 0 4.33 120.9a67.6 67.6 0 0 0 32.36.35a57.13 57.13 0 0 0 25.9-13.46a57.44 57.44 0 0 0 16-26.26a74.33 74.33 0 0 0 1.61-33.58H65.27v24.69h34.47a29.72 29.72 0 0 1-12.66 19.52a36.16 36.16 0 0 1-13.93 5.5a41.29 41.29 0 0 1-15.1 0A37.16 37.16 0 0 1 44 95.74a39.3 39.3 0 0 1-14.5-19.42a38.31 38.31 0 0 1 0-24.63a39.25 39.25 0 0 1 9.18-14.91A37.17 37.17 0 0 1 76.13 27a34.28 34.28 0 0 1 13.64 8q5.83-5.8 11.64-11.63c2-2.09 4.18-4.08 6.15-6.22A61.22 61.22 0 0 0 87.2 4.59a64 64 0 0 0-42.61-.38"
			/>
			<path
				fill="#e33629"
				d="M44.59 4.21a64 64 0 0 1 42.61.37a61.22 61.22 0 0 1 20.35 12.62c-2 2.14-4.11 4.14-6.15 6.22Q95.58 29.23 89.77 35a34.28 34.28 0 0 0-13.64-8a37.17 37.17 0 0 0-37.46 9.74a39.25 39.25 0 0 0-9.18 14.91L8.76 35.6A63.53 63.53 0 0 1 44.59 4.21"
			/>
			<path
				fill="#f8bd00"
				d="M3.26 51.5a62.93 62.93 0 0 1 5.5-15.9l20.73 16.09a38.31 38.31 0 0 0 0 24.63q-10.36 8-20.73 16.08a63.33 63.33 0 0 1-5.5-40.9"
			/>
			<path
				fill="#587dbd"
				d="M65.27 52.15h59.52a74.33 74.33 0 0 1-1.61 33.58a57.44 57.44 0 0 1-16 26.26c-6.69-5.22-13.41-10.4-20.1-15.62a29.72 29.72 0 0 0 12.66-19.54H65.27c-.01-8.22 0-16.45 0-24.68"
			/>
			<path
				fill="#319f43"
				d="M8.75 92.4q10.37-8 20.73-16.08A39.3 39.3 0 0 0 44 95.74a37.16 37.16 0 0 0 14.08 6.08a41.29 41.29 0 0 0 15.1 0a36.16 36.16 0 0 0 13.93-5.5c6.69 5.22 13.41 10.4 20.1 15.62a57.13 57.13 0 0 1-25.9 13.47a67.6 67.6 0 0 1-32.36-.35a63 63 0 0 1-23-11.59A63.73 63.73 0 0 1 8.75 92.4"
			/>
		</svg>
	);
};

export const LogoUS = ({ className }: { className?: string }) => {
	return (
		// <svg
		// 	xmlns="http://www.w3.org/2000/svg"
		// 	shapeRendering="geometricPrecision"
		// 	textRendering="geometricPrecision"
		// 	imageRendering="optimizeQuality"
		// 	fillRule="evenodd"
		// 	clipRule="evenodd"
		// 	viewBox="0 0 512 512"
		// 	className={className}
		// >
		// 	<rect fill="#CC9B7A" width="512" height="512" rx="104.187" ry="105.042" />
		// 	<path
		// 		fill="#1F1F1E"
		// 		fillRule="nonzero"
		// 		d="M318.663 149.787h-43.368l78.952 212.423 43.368.004-78.952-212.427zm-125.326 0l-78.952 212.427h44.255l15.932-44.608 82.846-.004 16.107 44.612h44.255l-79.126-212.427h-45.317zm-4.251 128.341l26.91-74.701 27.083 74.701h-53.993z"
		// 	/>
		// </svg>
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 512 512">
			<mask id="IconifyId195085577f173d4b72">
				<circle cx="256" cy="256" r="256" fill="#fff" />
			</mask>
			<g mask="url(#IconifyId195085577f173d4b72)">
				<path fill="#eee" d="M256 0h256v64l-32 32l32 32v64l-32 32l32 32v64l-32 32l32 32v64l-256 32L0 448v-64l32-32l-32-32v-64z" />
				<path fill="#d80027" d="M224 64h288v64H224Zm0 128h288v64H256ZM0 320h512v64H0Zm0 128h512v64H0Z" />
				<path fill="#0052b4" d="M0 0h256v256H0Z" />
				<path
					fill="#eee"
					d="m187 243l57-41h-70l57 41l-22-67zm-81 0l57-41H93l57 41l-22-67zm-81 0l57-41H12l57 41l-22-67zm162-81l57-41h-70l57 41l-22-67zm-81 0l57-41H93l57 41l-22-67zm-81 0l57-41H12l57 41l-22-67Zm162-82l57-41h-70l57 41l-22-67Zm-81 0l57-41H93l57 41l-22-67zm-81 0l57-41H12l57 41l-22-67Z"
				/>
			</g>
		</svg>
	);
};
export const LogoES = ({ className }: { className?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 512 512">
			<mask id="IconifyId195085577f173d4b7511">
				<circle cx="256" cy="256" r="256" fill="#fff" />
			</mask>
			<g mask="url(#IconifyId195085577f173d4b7511)">
				<path fill="#ffda44" d="m0 128l256-32l256 32v256l-256 32L0 384Z" />
				<path fill="#d80027" d="M0 0h512v128H0zm0 384h512v128H0z" />
				<g fill="#eee">
					<path d="M144 304h-16v-80h16zm128 0h16v-80h-16z" />
					<ellipse cx="208" cy="296" rx="48" ry="32" />
				</g>
				<g fill="#d80027">
					<rect width="16" height="24" x="128" y="192" rx="8" />
					<rect width="16" height="24" x="272" y="192" rx="8" />
					<path d="M208 272v24a24 24 0 0 0 24 24a24 24 0 0 0 24-24v-24h-24z" />
				</g>
				<rect width="32" height="16" x="120" y="208" fill="#ff9811" ry="8" />
				<rect width="32" height="16" x="264" y="208" fill="#ff9811" ry="8" />
				<rect width="32" height="16" x="120" y="304" fill="#ff9811" rx="8" />
				<rect width="32" height="16" x="264" y="304" fill="#ff9811" rx="8" />
				<path fill="#ff9811" d="M160 272v24c0 8 4 14 9 19l5-6l5 10a21 21 0 0 0 10 0l5-10l5 6c6-5 9-11 9-19v-24h-9l-5 8l-5-8h-10l-5 8l-5-8z" />
				<path
					fill="#d80027"
					d="M122 248a4 4 0 0 0-4 4a4 4 0 0 0 4 4h172a4 4 0 0 0 4-4a4 4 0 0 0-4-4zm0 24a4 4 0 0 0-4 4a4 4 0 0 0 4 4h28a4 4 0 0 0 4-4a4 4 0 0 0-4-4zm144 0a4 4 0 0 0-4 4a4 4 0 0 0 4 4h28a4 4 0 0 0 4-4a4 4 0 0 0-4-4z"
				/>
				<path
					fill="#eee"
					d="M196 168c-7 0-13 5-15 11l-5-1c-9 0-16 7-16 16s7 16 16 16c7 0 13-4 15-11a16 16 0 0 0 17-4a16 16 0 0 0 17 4a16 16 0 1 0 10-20a16 16 0 0 0-27-5q-4.5-6-12-6m0 8c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-8c0-4 4-8 8-8m24 0c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-8c0-4 4-8 8-8m-44 10l4 1l4 8c0 4-4 7-8 7s-8-3-8-8c0-4 4-8 8-8m64 0c5 0 8 4 8 8c0 5-3 8-8 8c-4 0-8-3-8-7l4-8z"
				/>
				<path fill="none" d="M220 284v12c0 7 5 12 12 12s12-5 12-12v-12z" />
				<path fill="#ff9811" d="M200 160h16v32h-16z" />
				<path fill="#eee" d="M208 224h48v48h-48z" />
				<path fill="#d80027" d="m248 208l-8 8h-64l-8-8c0-13 18-24 40-24s40 11 40 24m-88 16h48v48h-48z" />
				<rect width="20" height="32" x="222" y="232" fill="#d80027" rx="10" ry="10" />
				<path fill="#ff9811" d="M168 232v8h8v16h-8v8h32v-8h-8v-16h8v-8zm8-16h64v8h-64z" />
				<g fill="#ffda44">
					<circle cx="186" cy="202" r="6" />
					<circle cx="208" cy="202" r="6" />
					<circle cx="230" cy="202" r="6" />
				</g>
				<path fill="#d80027" d="M169 272v43a24 24 0 0 0 10 4v-47zm20 0v47a24 24 0 0 0 10-4v-43z" />
				<g fill="#338af3">
					<circle cx="208" cy="272" r="16" />
					<rect width="32" height="16" x="264" y="320" ry="8" />
					<rect width="32" height="16" x="120" y="320" ry="8" />
				</g>
			</g>
		</svg>
	);
};
export const LogoFR = ({ className }: { className?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 512 512">
			<mask id="IconifyId195085577f173d4b7515">
				<circle cx="256" cy="256" r="256" fill="#fff" />
			</mask>
			<g mask="url(#IconifyId195085577f173d4b7515)">
				<path fill="#eee" d="M167 0h178l25.9 252.3L345 512H167l-29.8-253.4z" />
				<path fill="#0052b4" d="M0 0h167v512H0z" />
				<path fill="#d80027" d="M345 0h167v512H345z" />
			</g>
		</svg>
	);
};
export const LogoDE = ({ className }: { className?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 512 512">
			<mask id="IconifyId195085577f173d4b7517">
				<circle cx="256" cy="256" r="256" fill="#fff" />
			</mask>
			<g mask="url(#IconifyId195085577f173d4b7517)">
				<path fill="#ffda44" d="m0 345l256.7-25.5L512 345v167H0z" />
				<path fill="#d80027" d="m0 167l255-23l257 23v178H0z" />
				<path fill="#333" d="M0 0h512v167H0z" />
			</g>
		</svg>
	);
};
export const LogoJP = ({ className }: { className?: string }) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} viewBox="0 0 512 512">
			<mask id="IconifyId195085577f173d4b7519">
				<circle cx="256" cy="256" r="256" fill="#fff" />
			</mask>
			<g mask="url(#IconifyId195085577f173d4b7519)">
				<path fill="#eee" d="M0 0h512v512H0z" />
				<circle cx="256" cy="256" r="111.3" fill="#d80027" />
			</g>
		</svg>
	);
};

export const ProIconsSubtitle = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={className} {...props}>
			<g fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5">
				<rect width="18.5" height="14.5" x="2.75" y="4.75" rx="4" />
				<path d="M10.5 14.382a2.75 2.75 0 1 1 0-4.764m7.125 4.764a2.75 2.75 0 1 1 0-4.764" />
			</g>
		</svg>
	);
};
export const ProIconsDocument = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} {...props} viewBox="0 0 24 24">
			<g fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5">
				<rect width="15" height="18.5" x="4.5" y="2.75" rx="3.5" />
				<path d="M8.5 6.755h7m-7 4h7m-7 4H12" />
			</g>
		</svg>
	);
};

export const IconsMic = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} {...props} viewBox="0 0 24 24">
			<path
				fill="currentColor"
				d="M12 14q-1.25 0-2.125-.875T9 11V5q0-1.25.875-2.125T12 2t2.125.875T15 5v6q0 1.25-.875 2.125T12 14m-1 6v-2.075q-2.3-.325-3.937-1.95t-1.988-3.95q-.05-.425.225-.725T6 11t.713.288T7.1 12q.35 1.75 1.738 2.875T12 16q1.8 0 3.175-1.137T16.9 12q.1-.425.388-.712T18 11t.7.3t.225.725q-.35 2.275-1.975 3.925T13 17.925V20q0 .425-.288.713T12 21t-.712-.288T11 20"
			/>
		</svg>
	);
};

export const IconsDocument = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg xmlns="http://www.w3.org/2000/svg" className={className} {...props} viewBox="0 0 24 24">
			<path
				fill="currentColor"
				fillRule="evenodd"
				d="M4.172 3.172C3 4.343 3 6.229 3 10v4c0 3.771 0 5.657 1.172 6.828S7.229 22 11 22h2c3.771 0 5.657 0 6.828-1.172S21 17.771 21 14v-4c0-3.771 0-5.657-1.172-6.828S16.771 2 13 2h-2C7.229 2 5.343 2 4.172 3.172M7.25 8A.75.75 0 0 1 8 7.25h8a.75.75 0 0 1 0 1.5H8A.75.75 0 0 1 7.25 8m0 4a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M8 15.25a.75.75 0 0 0 0 1.5h5a.75.75 0 0 0 0-1.5z"
				clipRule="evenodd"
			/>
		</svg>
	);
};

export const CustomIconsTXT = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg viewBox="0 0 70 80" fill="none" xmlns="http://www.w3.org/2000/svg" className={cn("size-16", className)} {...props}>
			<g clipPath="url(#clip0_63_2222)">
				<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#344054" />
				<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" fillOpacity="0.5" />
			</g>
			<path
				d="M11.1024 52.9901V50.4545H23.0484V52.9901H18.5953V65H15.5555V52.9901H11.1024ZM27.4993 50.4545L30.4325 55.4119H30.5462L33.4936 50.4545H36.9666L32.5277 57.7273L37.0661 65H33.5291L30.5462 60.0355H30.4325L27.4496 65H23.9268L28.4794 57.7273L24.0121 50.4545H27.4993ZM37.9587 52.9901V50.4545H49.9047V52.9901H45.4516V65H42.4118V52.9901H37.9587Z"
				fill="white"
			/>
			<defs>
				<clipPath id="clip0_63_2222">
					<rect width="60" height="80" fill="white" />
				</clipPath>
			</defs>
		</svg>
	);
};

export const CustomIconsMP3 = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M13.2773 66H10.4746V51.6055H14.8496L17.4668 62.9238L20.0645 51.6055H24.3906V66H21.5879V56.2637C21.5879 55.9837 21.5911 55.5931 21.5977 55.0918C21.6042 54.584 21.6074 54.1934 21.6074 53.9199L18.8828 66H15.9629L13.2578 53.9199C13.2578 54.1934 13.2611 54.584 13.2676 55.0918C13.2741 55.5931 13.2773 55.9837 13.2773 56.2637V66ZM33.2969 60.8242H30.2402V66H27.252V51.6055H33.5215C34.9668 51.6055 36.1191 51.9766 36.9785 52.7188C37.8379 53.4609 38.2676 54.61 38.2676 56.166C38.2676 57.8652 37.8379 59.0664 36.9785 59.7695C36.1191 60.4727 34.8919 60.8242 33.2969 60.8242ZM34.7031 57.8262C35.0938 57.4811 35.2891 56.9342 35.2891 56.1855C35.2891 55.4368 35.0905 54.903 34.6934 54.584C34.3027 54.265 33.7526 54.1055 33.043 54.1055H30.2402V58.3438H33.043C33.7526 58.3438 34.306 58.1712 34.7031 57.8262ZM42.8281 54.6621C42.5156 55.0788 42.3659 55.6354 42.3789 56.332H39.7812C39.8073 55.6289 39.9277 54.9616 40.1426 54.3301C40.3704 53.7767 40.7285 53.2656 41.2168 52.7969C41.5814 52.4648 42.0143 52.2109 42.5156 52.0352C43.0169 51.8594 43.6322 51.7715 44.3613 51.7715C45.7155 51.7715 46.806 52.123 47.6328 52.8262C48.4661 53.5228 48.8828 54.4603 48.8828 55.6387C48.8828 56.472 48.6354 57.1751 48.1406 57.748C47.8281 58.1061 47.5026 58.3503 47.1641 58.4805C47.418 58.4805 47.7826 58.6986 48.2578 59.1348C48.9674 59.7923 49.3223 60.6908 49.3223 61.8301C49.3223 63.028 48.9056 64.0827 48.0723 64.9941C47.2454 65.8991 46.0182 66.3516 44.3906 66.3516C42.3854 66.3516 40.9922 65.6973 40.2109 64.3887C39.8008 63.6921 39.5729 62.7806 39.5273 61.6543H42.2617C42.2617 62.2207 42.3529 62.6895 42.5352 63.0605C42.8737 63.7441 43.4889 64.0859 44.3809 64.0859C44.9277 64.0859 45.403 63.9004 45.8066 63.5293C46.2168 63.1517 46.4219 62.6113 46.4219 61.9082C46.4219 60.9772 46.0443 60.3555 45.2891 60.043C44.8594 59.8672 44.1823 59.7793 43.2578 59.7793V57.7871C44.1628 57.7741 44.7943 57.6862 45.1523 57.5234C45.7708 57.25 46.0801 56.6966 46.0801 55.8633C46.0801 55.3229 45.9206 54.8835 45.6016 54.5449C45.2891 54.2064 44.8464 54.0371 44.2734 54.0371C43.6159 54.0371 43.1341 54.2454 42.8281 54.6621Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsMAV = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M17.9512 63.041H12.6484L11.6523 66H8.50781L13.6445 51.6055H17.043L22.1406 66H18.8789L17.9512 63.041ZM17.1113 60.5605L15.3145 54.8965L13.459 60.5605H17.1113ZM32.4043 63.041H27.1016L26.1055 66H22.9609L28.0977 51.6055H31.4961L36.5938 66H33.332L32.4043 63.041ZM31.5645 60.5605L29.7676 54.8965L27.9121 60.5605H31.5645ZM48.5859 64.9258C47.5117 65.9089 46.138 66.4004 44.4648 66.4004C42.3945 66.4004 40.7669 65.7363 39.582 64.4082C38.3971 63.0736 37.8047 61.2441 37.8047 58.9199C37.8047 56.4069 38.4785 54.4701 39.8262 53.1094C40.998 51.9245 42.4889 51.332 44.2988 51.332C46.7207 51.332 48.4915 52.1263 49.6113 53.7148C50.2298 54.6068 50.5618 55.502 50.6074 56.4004H47.5996C47.4043 55.7103 47.1536 55.1895 46.8477 54.8379C46.3008 54.2129 45.4902 53.9004 44.416 53.9004C43.3223 53.9004 42.4596 54.3431 41.8281 55.2285C41.1966 56.1074 40.8809 57.3542 40.8809 58.9688C40.8809 60.5833 41.2129 61.7943 41.877 62.6016C42.5475 63.4023 43.3971 63.8027 44.4258 63.8027C45.4805 63.8027 46.2845 63.4577 46.8379 62.7676C47.1439 62.3965 47.3978 61.8398 47.5996 61.0977H50.5781C50.3177 62.6667 49.6536 63.9427 48.5859 64.9258Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsAAC = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M17.9512 63.041H12.6484L11.6523 66H8.50781L13.6445 51.6055H17.043L22.1406 66H18.8789L17.9512 63.041ZM17.1113 60.5605L15.3145 54.8965L13.459 60.5605H17.1113ZM32.4043 63.041H27.1016L26.1055 66H22.9609L28.0977 51.6055H31.4961L36.5938 66H33.332L32.4043 63.041ZM31.5645 60.5605L29.7676 54.8965L27.9121 60.5605H31.5645ZM48.5859 64.9258C47.5117 65.9089 46.138 66.4004 44.4648 66.4004C42.3945 66.4004 40.7669 65.7363 39.582 64.4082C38.3971 63.0736 37.8047 61.2441 37.8047 58.9199C37.8047 56.4069 38.4785 54.4701 39.8262 53.1094C40.998 51.9245 42.4889 51.332 44.2988 51.332C46.7207 51.332 48.4915 52.1263 49.6113 53.7148C50.2298 54.6068 50.5618 55.502 50.6074 56.4004H47.5996C47.4043 55.7103 47.1536 55.1895 46.8477 54.8379C46.3008 54.2129 45.4902 53.9004 44.416 53.9004C43.3223 53.9004 42.4596 54.3431 41.8281 55.2285C41.1966 56.1074 40.8809 57.3542 40.8809 58.9688C40.8809 60.5833 41.2129 61.7943 41.877 62.6016C42.5475 63.4023 43.3971 63.8027 44.4258 63.8027C45.4805 63.8027 46.2845 63.4577 46.8379 62.7676C47.1439 62.3965 47.3978 61.8398 47.5996 61.0977H50.5781C50.3177 62.6667 49.6536 63.9427 48.5859 64.9258Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsM4A = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M12.2773 66H9.47461V51.6055H13.8496L16.4668 62.9238L19.0645 51.6055H23.3906V66H20.5879V56.2637C20.5879 55.9837 20.5911 55.5931 20.5977 55.0918C20.6042 54.584 20.6074 54.1934 20.6074 53.9199L17.8828 66H14.9629L12.2578 53.9199C12.2578 54.1934 12.2611 54.584 12.2676 55.0918C12.2741 55.5931 12.2773 55.9837 12.2773 56.2637V66ZM35.1191 60.7754V62.9531H33.5176V66H30.793V62.9531H25.1875V60.5215L30.3926 51.9277H33.5176V60.7754H35.1191ZM27.248 60.7754H30.793V54.6621L27.248 60.7754ZM45.7441 63.041H40.4414L39.4453 66H36.3008L41.4375 51.6055H44.8359L49.9336 66H46.6719L45.7441 63.041ZM44.9043 60.5605L43.1074 54.8965L41.252 60.5605H44.9043Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsMP4 = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M11.2773 66H8.47461V51.6055H12.8496L15.4668 62.9238L18.0645 51.6055H22.3906V66H19.5879V56.2637C19.5879 55.9837 19.5911 55.5931 19.5977 55.0918C19.6042 54.584 19.6074 54.1934 19.6074 53.9199L16.8828 66H13.9629L11.2578 53.9199C11.2578 54.1934 11.2611 54.584 11.2676 55.0918C11.2741 55.5931 11.2773 55.9837 11.2773 56.2637V66ZM31.5508 66.4004C29.4935 66.4004 27.9212 65.8405 26.834 64.7207C25.3757 63.347 24.6465 61.3678 24.6465 58.7832C24.6465 56.1465 25.3757 54.1673 26.834 52.8457C27.9212 51.7259 29.4935 51.166 31.5508 51.166C33.6081 51.166 35.1803 51.7259 36.2676 52.8457C37.7194 54.1673 38.4453 56.1465 38.4453 58.7832C38.4453 61.3678 37.7194 63.347 36.2676 64.7207C35.1803 65.8405 33.6081 66.4004 31.5508 66.4004ZM34.3926 62.5332C35.0892 61.6543 35.4375 60.4043 35.4375 58.7832C35.4375 57.1686 35.0859 55.9219 34.3828 55.043C33.6862 54.1576 32.7422 53.7148 31.5508 53.7148C30.3594 53.7148 29.4089 54.1543 28.6992 55.0332C27.9896 55.9121 27.6348 57.1621 27.6348 58.7832C27.6348 60.4043 27.9896 61.6543 28.6992 62.5332C29.4089 63.4121 30.3594 63.8516 31.5508 63.8516C32.7422 63.8516 33.6895 63.4121 34.3926 62.5332ZM39.6855 51.6055H42.8984L46.0234 62.5332L49.1777 51.6055H52.3027L47.3906 66H44.5488L39.6855 51.6055Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsMOV = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M11.2773 66H8.47461V51.6055H12.8496L15.4668 62.9238L18.0645 51.6055H22.3906V66H19.5879V56.2637C19.5879 55.9837 19.5911 55.5931 19.5977 55.0918C19.6042 54.584 19.6074 54.1934 19.6074 53.9199L16.8828 66H13.9629L11.2578 53.9199C11.2578 54.1934 11.2611 54.584 11.2676 55.0918C11.2741 55.5931 11.2773 55.9837 11.2773 56.2637V66ZM31.5508 66.4004C29.4935 66.4004 27.9212 65.8405 26.834 64.7207C25.3757 63.347 24.6465 61.3678 24.6465 58.7832C24.6465 56.1465 25.3757 54.1673 26.834 52.8457C27.9212 51.7259 29.4935 51.166 31.5508 51.166C33.6081 51.166 35.1803 51.7259 36.2676 52.8457C37.7194 54.1673 38.4453 56.1465 38.4453 58.7832C38.4453 61.3678 37.7194 63.347 36.2676 64.7207C35.1803 65.8405 33.6081 66.4004 31.5508 66.4004ZM34.3926 62.5332C35.0892 61.6543 35.4375 60.4043 35.4375 58.7832C35.4375 57.1686 35.0859 55.9219 34.3828 55.043C33.6862 54.1576 32.7422 53.7148 31.5508 53.7148C30.3594 53.7148 29.4089 54.1543 28.6992 55.0332C27.9896 55.9121 27.6348 57.1621 27.6348 58.7832C27.6348 60.4043 27.9896 61.6543 28.6992 62.5332C29.4089 63.4121 30.3594 63.8516 31.5508 63.8516C32.7422 63.8516 33.6895 63.4121 34.3926 62.5332ZM39.6855 51.6055H42.8984L46.0234 62.5332L49.1777 51.6055H52.3027L47.3906 66H44.5488L39.6855 51.6055Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsAVI = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M13.2773 66H10.4746V51.6055H14.8496L17.4668 62.9238L20.0645 51.6055H24.3906V66H21.5879V56.2637C21.5879 55.9837 21.5911 55.5931 21.5977 55.0918C21.6042 54.584 21.6074 54.1934 21.6074 53.9199L18.8828 66H15.9629L13.2578 53.9199C13.2578 54.1934 13.2611 54.584 13.2676 55.0918C13.2741 55.5931 13.2773 55.9837 13.2773 56.2637V66ZM36.1191 60.7754V62.9531H34.5176V66H31.793V62.9531H26.1875V60.5215L31.3926 51.9277H34.5176V60.7754H36.1191ZM28.248 60.7754H31.793V54.6621L28.248 60.7754ZM37.252 51.6055H40.4648L43.5898 62.5332L46.7441 51.6055H49.8691L44.957 66H42.1152L37.252 51.6055Z"
				fill="white"
			/>
		</svg>
	);
};
export const CustomIconsM4V = ({ className, ...props }: ComponentProps<"svg">) => {
	return (
		<svg className={cn("size-16", className)} {...props} viewBox="0 0 60 80" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M0 8C0 3.58172 3.58172 0 8 0H34L60 26V72C60 76.4183 56.4183 80 52 80H8C3.58172 80 0 76.4183 0 72V8Z" fill="#3B82F6" />
			<path d="M40 26H60L34 0V20C34 23.3137 36.6863 26 40 26Z" fill="white" />
			<path
				d="M13.2773 66H10.4746V51.6055H14.8496L17.4668 62.9238L20.0645 51.6055H24.3906V66H21.5879V56.2637C21.5879 55.9837 21.5911 55.5931 21.5977 55.0918C21.6042 54.584 21.6074 54.1934 21.6074 53.9199L18.8828 66H15.9629L13.2578 53.9199C13.2578 54.1934 13.2611 54.584 13.2676 55.0918C13.2741 55.5931 13.2773 55.9837 13.2773 56.2637V66ZM36.1191 60.7754V62.9531H34.5176V66H31.793V62.9531H26.1875V60.5215L31.3926 51.9277H34.5176V60.7754H36.1191ZM28.248 60.7754H31.793V54.6621L28.248 60.7754ZM37.252 51.6055H40.4648L43.5898 62.5332L46.7441 51.6055H49.8691L44.957 66H42.1152L37.252 51.6055Z"
				fill="white"
			/>
		</svg>
	);
};
