"use client";

import { GoogleTagManager } from "@next/third-parties/google";
import { GTM_TOKEN, WEB_HOST } from "@/lib/constants";
import { usePathname } from "next/navigation";

export function AnalyticsGoolge() {
	const pathname = usePathname();
	if (pathname.startsWith("/admin")) {
		console.log("skip admin page");
		return null;
	}
	return (
		<GoogleTagManager gtmId={GTM_TOKEN} gtmScriptUrl={`https://${WEB_HOST}/zdata/`} />
		// <div className="container">
		// 	<Script src={`https://www.googletagmanager.com/gtag/js?id=${GA_TOKEN}`} />
		// 	<Script id="google-analytics">
		// 		{`
		// 			window.dataLayer = window.dataLayer || [];
		// 			function gtag(){dataLayer.push(arguments);}
		// 			gtag('js', new Date());

		// 			gtag('config', '${GA_TOKEN}');
		// 		`}
		// 	</Script>
		// </div>
	);
}
