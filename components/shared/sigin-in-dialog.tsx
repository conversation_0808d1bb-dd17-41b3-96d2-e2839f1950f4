"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import SignIn from "./sigin-in";

export default function SignInDialog() {
	const { signInBoxOpen, setSignInBoxOpen } = useSignInBoxOpenStore();

	return (
		<Dialog open={signInBoxOpen} onOpenChange={setSignInBoxOpen}>
			<DialogContent className="max-w-sm rounded-lg">
				<DialogTitle></DialogTitle>
				<SignIn />
			</DialogContent>
		</Dialog>
	);
}
