"use client";

import { useEffect } from "react";
import { useUserStore } from "@/store/useUserStore";
// import { useCookies } from "next-client-cookies";
// import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
// import { useSession } from "@/lib/auth-client";
import { UserInfoDB } from "@/@types/user";

export function InitializeUser({ userInfo }: { userInfo: UserInfoDB | null }) {
	// const { data: session, isPending } = useSession();
	// const cookies = useCookies();
	// const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();
	const { user, setUser, refreshUser } = useUserStore();

	// useEffect(() => {
	// 	console.log("isPending:", isPending);
	// 	if (isPending) return;
	// 	if (session && !user) {
	// 		refreshUser();
	// 	}
	// }, [isPending]);

	useEffect(() => {
		setUser(userInfo);
	}, []);

	return <></>;
}
