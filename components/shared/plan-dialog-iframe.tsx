"use client";

import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PricingOnetime, pricingOnetime } from "@/config/pricing";
import { cn } from "@/lib/utils";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Check, Circle, CircleCheck, Loader2, ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { Button, buttonVariants } from "../ui/button";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, handleError } from "@/@types/error";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useRouter } from "nextjs-toploader/app";
import { sendGTMEvent } from "@next/third-parties/google";
import { GA_EVENT_CHECKOUT } from "@/lib/google-tag-events";

export default function PlanDialog() {
	const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const router = useRouter();

	const [selectProductId, setSelectProductId] = useState<string>(pricingOnetime[2].productId);
	const [selectPrice, setSelectPrice] = useState<string>(`${pricingOnetime[2].currency.symbol}${pricingOnetime[2].price}`);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const [checkoutUrl, setCheckoutUrl] = useState<string>("");
	const [showCheckout, setShowCheckout] = useState(false);

	const purchase = async (productId: string) => {
		console.log("New checkout");

		sendGTMEvent({ event: GA_EVENT_CHECKOUT, checkout_plan: selectPrice });

		try {
			setIsPurchasing(true);
			const { status, message, url } = await ofetch("/api/payment/checkout", { method: "POST", body: { productId, type: "onetime" } });
			handleError(status, message);
			if (url) {
				setCheckoutUrl(url);
				setShowCheckout(true);
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Failed to purchase. Please try again.");
			}
		} finally {
			setIsPurchasing(false);
		}
	};

	const handleBackToPricing = () => {
		setShowCheckout(false);
		setCheckoutUrl("");
	};

	// Reset state when dialog closes
	useEffect(() => {
		if (!planBoxOpen) {
			setShowCheckout(false);
			setCheckoutUrl("");
			setIsPurchasing(false);
		}
	}, [planBoxOpen]);

	return (
		<Dialog open={planBoxOpen} onOpenChange={setPlanBoxOpen}>
			<DialogContent className={cn("gap-0 rounded-xl p-0 sm:rounded-xl", showCheckout ? "sm:max-w-4xl" : "sm:max-w-max")}>
				<DialogTitle />
				<ScrollArea className="max-h-[90vh] overflow-y-auto">
					{showCheckout ? (
						// Checkout iframe view
						<div className="flex flex-col">
							<div className="flex items-center gap-2 border-b p-4">
								<Button variant="ghost" size="sm" onClick={handleBackToPricing} className="flex items-center gap-2">
									<ArrowLeft className="h-4 w-4" />
									Back to Pricing
								</Button>
								<div className="flex-1 text-center">
									<p className="text-lg font-semibold">Checkout</p>
								</div>
							</div>
							<div className="p-4">
								<div className="mb-4 rounded-lg bg-gray-50 p-3">
									<p className="text-sm text-gray-600">Checkout URL:</p>
									<p className="font-mono text-sm break-all">{checkoutUrl}</p>
								</div>
								<div className="h-[600px] w-full rounded-lg border">
									<iframe
										src={checkoutUrl}
										className="h-full w-full rounded-lg"
										title="Checkout"
										sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
									/>
								</div>
							</div>
						</div>
					) : (
						// Original pricing view
						<div className="flex flex-col items-center">
							{/* Plan */}
							<div className="flex w-full flex-col sm:flex-row">
								{/* Left side: 3-column pricing table */}
								<div className="order-2 w-full rounded-lg bg-zinc-100 p-6 sm:order-1 sm:w-72">
									<div className="flex w-full flex-col gap-4 text-sm">
										<div className="flex w-full flex-row items-center justify-between gap-2">
											<p className="text-lg font-semibold">Supported Features</p>
										</div>
										<div className="flex w-full flex-row items-center justify-between gap-2">
											<p>20+ Style Presets</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>High resolution</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>Fastest generation</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>No watermark</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>Commercial use</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>Private generation</p>
											<Check className="size-4 text-green-500" />
										</div>
										<div className="flex flex-row items-center justify-between gap-2">
											<p>Valid for 1 month</p>
											<Check className="size-4 text-green-500" />
										</div>
									</div>
								</div>

								{/* Right side: Selection and purchase */}
								<div className="order-1 w-full space-y-3 p-6 sm:order-2 sm:w-[340px]">
									<p className="mx-auto w-full text-center text-2xl font-semibold">Get more credits</p>
									{pricingOnetime.map((pricing: PricingOnetime, index: number) => {
										if (pricing.free) return null;
										return (
											<div key={index} className="relative">
												<button
													onClick={() => {
														setSelectProductId(pricing.productId);
														setSelectPrice(`${pricing.currency.symbol}${pricing.price}`);
													}}
													className={cn(
														"flex w-full flex-row items-center justify-between gap-2 rounded-lg border-2 px-4 py-4",
														selectProductId === pricing.productId && "border-teal-500 bg-teal-50",
													)}
												>
													<div className="flex flex-row items-center gap-2">
														{selectProductId === pricing.productId ? (
															<CircleCheck className="size-6 text-teal-500" />
														) : (
															<Circle className="size-6 text-teal-500" />
														)}
														<p className="text-lg">{pricing.credits} Images</p>
													</div>
													<p className="text-xl font-medium">
														{pricing.currency.symbol}
														{pricing.price}
													</p>
												</button>
												{pricing.badge && (
													<p className="absolute top-0 right-0 -translate-y-1/2 rounded-full bg-teal-500 px-2.5 py-0.5 text-xs font-normal text-white">
														{pricing.badge}
													</p>
												)}
											</div>
										);
									})}

									<Button
										size="lg"
										className="h-[44px] w-full rounded-full bg-teal-500 hover:bg-teal-600"
										onClick={() => {
											purchase(selectProductId);
										}}
										disabled={isPurchasing}
									>
										Buy Now
										{isPurchasing && <Loader2 className="h-4 w-4 animate-spin" />}
									</Button>
								</div>
							</div>
						</div>
					)}
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}
