"use client";

import { type LucideIcon } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
	SidebarGroup,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

export function NavMain({
	items,
}: {
	items: {
		title: string;
		url?: string;
		icon?: LucideIcon;
		isActive?: boolean;
		items?: {
			title: string;
			url: string;
		}[];
	}[];
}) {
	const pathname = usePathname();
	return (
		<SidebarGroup>
			<SidebarMenu className="space-y-1">
				{items.map((item, index) =>
					item.url ? (
						<SidebarMenuItem key={index}>
							<NoPrefetchLink href={item.url}>
								<SidebarMenuButton tooltip={item.title} isActive={pathname === item.url}>
									{item.icon && <item.icon />}
									<span className="ml-1 font-medium">{item.title}</span>
								</SidebarMenuButton>
							</NoPrefetchLink>
						</SidebarMenuItem>
					) : (
						<Collapsible key={index} asChild defaultOpen={item.isActive} className="group/collapsible">
							<SidebarMenuItem>
								<CollapsibleTrigger asChild>
									<SidebarMenuButton tooltip={item.title}>
										{item.icon && <item.icon />}
										<span className="ml-1 font-medium">{item.title}</span>
										{/* <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" /> */}
									</SidebarMenuButton>
								</CollapsibleTrigger>
								<CollapsibleContent>
									<SidebarMenuSub>
										{item.items?.map((subItem, subIndex) => (
											<SidebarMenuSubItem key={subIndex}>
												<SidebarMenuSubButton asChild isActive={pathname === subItem.url}>
													<a href={subItem.url}>
														<span>{subItem.title}</span>
													</a>
												</SidebarMenuSubButton>
											</SidebarMenuSubItem>
										))}
									</SidebarMenuSub>
								</CollapsibleContent>
							</SidebarMenuItem>
						</Collapsible>
					),
				)}
			</SidebarMenu>
		</SidebarGroup>
	);
}
