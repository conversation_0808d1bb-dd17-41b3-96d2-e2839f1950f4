"use client";

import { usePathname } from "next/navigation";
import { footerBadges } from "@/@types/footer-badge";

export default function FooterBadge() {
	const pathname = usePathname();

	if (pathname !== "/") return null;

	return (
		<div className="bg-zinc-50 pt-4">
			<div className="container flex flex-col gap-8 pb-6">
				<div className="flex flex-wrap justify-center gap-4">
					{footerBadges.map((badge, index) => (
						<a key={index} className="h-6 shrink-0" target="_blank" href={badge.href} rel="noopener noreferrer">
							<img className="h-full" src={badge.src} alt={badge.alt} loading="lazy" />
						</a>
					))}
					<a className="h-6 shrink-0" target="_blank" href="https://www.websitecarbon.com/website/youstylize-com/" rel="noopener noreferrer">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 180 180" className="size-6">
							<path
								d="M0 0 C59.4 0 118.8 0 180 0 C180 59.4 180 118.8 180 180 C120.6 180 61.2 180 0 180 C0 120.6 0 61.2 0 0 Z "
								fill="#181CD3"
								transform="translate(0,0)"
							/>
							<path
								d="M0 0 C59.4 0 118.8 0 180 0 C180 59.4 180 118.8 180 180 C120.6 180 61.2 180 0 180 C0 120.6 0 61.2 0 0 Z M55.938 13.688 C55.261 14.037 54.584 14.387 53.886 14.747 C32.311 26.165 17.993 44.711 8.762 66.988 C3.752 83.394 5.934 102.698 13.75 117.688 C21.572 131.916 33.031 144.583 47 153 C47.817 153.495 47.817 153.495 48.651 154 C65.553 164.065 84.021 168.255 103.5 163.812 C124.184 157.578 141.494 145.772 153.52 127.719 C154.008 127.152 154.497 126.584 155 126 C157 125.959 159 125.957 161 126 C163.35 122.476 163.244 121.145 163 117 C162.693 115.588 162.693 115.588 162.379 114.148 C161.95 110.582 162.497 108.775 163.75 105.438 C165.733 99.447 166.861 93.557 167.75 87.312 C167.941 86.013 167.941 86.013 168.136 84.687 C170.303 68.685 168.633 51.473 159 38 C148.123 23.887 134.416 17.026 117.255 13.431 C112.209 12.314 112.209 12.314 110 9 C103.401 6.258 95.692 6.666 88.688 6.688 C87.636 6.671 86.585 6.655 85.502 6.639 C74.925 6.632 65.313 8.759 55.938 13.688 Z "
								fill="#FCFDFE"
								transform="translate(0,0)"
							/>
							<path
								d="M0 0 C3.73 0.132 7.461 0.024 11.191 -0.039 C16.23 -0.09 21.012 0.273 26 1 C25.472 1.692 25.472 1.692 24.934 2.398 C23.275 4.589 21.638 6.794 20 9 C19.412 9.771 18.824 10.542 18.219 11.336 C12.544 19.026 7.188 28.203 7 38 C7.656 41.778 8.788 45.364 10 49 C17.769 52.013 24.704 51.188 32.656 49.445 C35.469 48.911 38.145 48.85 41 49 C39.58 52.09 37.869 54.596 35.75 57.25 C35.133 58.032 34.515 58.815 33.879 59.621 C33.259 60.406 32.639 61.191 32 62 C30.89 63.433 29.785 64.87 28.688 66.312 C27.982 67.235 27.982 67.235 27.262 68.176 C24.182 72.629 23.135 76.564 24 82 C26.581 86.567 29.074 87.628 34 89 C36.016 89.246 38.042 89.42 40.07 89.527 C41.787 89.633 41.787 89.633 43.539 89.74 C45.906 89.87 48.273 89.998 50.641 90.123 C58.393 90.604 65.538 91.839 73 94 C69.089 107.581 56.049 118.368 44.277 125.277 C39.658 127.705 35.009 129.553 30 131 C28.153 131.541 28.153 131.541 26.27 132.094 C21.662 133.226 17.221 133.225 12.5 133.125 C11.678 133.116 10.855 133.107 10.008 133.098 C8.005 133.074 6.002 133.039 4 133 C3.211 130.91 3.211 130.91 3 128 C4.457 125.618 5.889 123.596 7.625 121.438 C16.239 110.121 19.952 99.213 19 85 C17.903 79.935 15.731 75.627 12 72 C5.664 68.172 -1.991 67.818 -9.173 69.513 C-9.776 69.674 -10.379 69.834 -11 70 C-11.037 68.782 -11.075 67.564 -11.113 66.309 C-11.179 64.685 -11.245 63.061 -11.312 61.438 C-11.346 60.238 -11.346 60.238 -11.381 59.014 C-11.651 52.987 -12.581 47.903 -16.562 43.125 C-19.975 40.454 -22.745 40.588 -27 41 C-40.548 46.544 -48.286 62.247 -53.762 74.992 C-54.17 75.985 -54.579 76.977 -55 78 C-55.33 78 -55.66 78 -56 78 C-59.846 60.692 -54.109 44.765 -44.896 30.262 C-35.628 16.539 -18.285 -3.303 0 0 Z "
								fill="#FCFDFE"
								transform="translate(74,21)"
							/>
							<path
								d="M0 0 C11.647 4.415 21.145 10.982 27.145 22.082 C33.051 35.359 33.292 48.876 30.832 63.02 C30.714 63.704 30.597 64.389 30.476 65.095 C29.546 70 27.994 74.446 26.145 79.082 C25.219 78.816 24.293 78.551 23.34 78.277 C10.1 74.719 -3.206 73.856 -16.855 73.082 C-17.185 72.092 -17.515 71.102 -17.855 70.082 C-16.409 67.883 -14.953 65.921 -13.293 63.895 C-12.366 62.727 -11.439 61.559 -10.512 60.391 C-10.057 59.825 -9.602 59.259 -9.133 58.675 C-7.489 56.625 -5.883 54.549 -4.293 52.457 C-3.838 51.867 -3.383 51.276 -2.914 50.668 C-0.986 47.779 -0.651 45.026 -0.543 41.645 C-0.506 40.883 -0.468 40.121 -0.43 39.336 C-1.041 36.098 -2.208 35.004 -4.855 33.082 C-12.102 32.005 -19.593 32.878 -26.672 34.645 C-29.129 35.137 -31.356 35.219 -33.855 35.082 C-34.185 34.752 -34.515 34.422 -34.855 34.082 C-34.641 20.765 -26.078 10.382 -17.039 1.246 C-11.373 -3.513 -6.464 -2.078 0 0 Z "
								fill="#FCFDFE"
								transform="translate(125.85546875,26.91796875)"
							/>
							<path
								d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.22 14.503 3.199 27.648 1 41 C1.713 40.397 2.426 39.793 3.16 39.172 C17.487 27.217 17.487 27.217 25.914 27.605 C29.11 28.21 30.838 29.635 33 32 C35.663 39.99 34.102 47.382 30.699 54.879 C28.186 59.495 25.217 63.679 22.086 67.895 C19.86 71.208 18.458 74.299 17 78 C2.103 73.498 -14.06 60.461 -21.75 46.918 C-24.704 40.023 -21.114 32.382 -18.652 25.738 C-15.15 17.36 -8.244 4.122 0 0 Z "
								fill="#FCFDFE"
								transform="translate(49,72)"
							/>
						</svg>
					</a>
				</div>
			</div>
		</div>
	);
}
