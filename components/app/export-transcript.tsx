"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
	DropdownMenuSeparator,
	DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Download, FileText, FileImage, Hash, Subtitles, FileSpreadsheet, ChevronDown } from "lucide-react";
import { toast } from "sonner";
import { exportTranscript, ExportFormat } from "@/lib/file/export-utils";

interface ExportDropdownProps {
	sentences: SentenceData[];
	filename?: string;
	languageCode: string;
	userHasPaid: boolean;
	disabled?: boolean;
	variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
	size?: "default" | "sm" | "lg" | "icon";
}

const exportOptions = [
	{
		format: "txt" as ExportFormat,
		label: "Plain Text",
		description: "Simple text file",
		icon: FileText,
	},
	{
		format: "pdf" as ExportFormat,
		label: "PDF Document",
		description: "Formatted PDF file",
		icon: FileImage,
	},
	{
		format: "md" as ExportFormat,
		label: "Markdown",
		description: "Markdown format",
		icon: Hash,
	},
	{
		format: "srt" as ExportFormat,
		label: "SRT Subtitles",
		description: "Subtitle file with timestamps",
		icon: Subtitles,
	},
	{
		format: "docx" as ExportFormat,
		label: "Word Document",
		description: "Microsoft Word format",
		icon: FileSpreadsheet,
	},
];

export function ExportDropdown({
	sentences,
	filename = "transcript",
	languageCode,
	userHasPaid,
	disabled = false,
	variant = "secondary",
	size = "default",
}: ExportDropdownProps) {
	const [isExporting, setIsExporting] = useState(false);

	const handleExport = async (format: ExportFormat) => {
		if (isExporting || !sentences || sentences.length === 0) return;

		try {
			setIsExporting(true);
			await exportTranscript(sentences, format, languageCode, filename);
		} catch (error) {
			console.error("Export failed:", error);
			toast.error(`Failed to export as ${format.toUpperCase()}`);
		} finally {
			setIsExporting(false);
		}
	};

	const hasContent = sentences && sentences.length > 0;

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant={variant} size={size} disabled={disabled || !hasContent || isExporting} className="gap-2">
					<Download className="h-4 w-4" />
					{isExporting ? "Exporting..." : "Export"}
					<ChevronDown className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-56">
				<DropdownMenuLabel>Export Format</DropdownMenuLabel>
				<DropdownMenuSeparator />
				{exportOptions.map((option) => {
					const Icon = option.icon;
					return (
						<DropdownMenuItem
							key={option.format}
							onClick={() => {
								if (!userHasPaid) {
									toast.warning("You need to upgrade to export transcripts.");
									return;
								}
								handleExport(option.format);
							}}
							disabled={isExporting}
							className="cursor-pointer"
						>
							<Icon className="mr-2 h-4 w-4" />
							<div className="flex flex-col">
								<span className="font-medium">{option.label}</span>
								<span className="text-muted-foreground text-xs">{option.description}</span>
							</div>
						</DropdownMenuItem>
					);
				})}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
