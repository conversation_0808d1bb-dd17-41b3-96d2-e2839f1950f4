import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";
import { formatTime } from "@/lib/utils-date";
import { MediaActionTypes, useMediaDispatch, useMediaSelector } from "media-chrome/react/media-store";
import { Play, User } from "lucide-react";

function binarySearchSentence(sentences: SentenceData[], currentTime: number | null | undefined): { index: number; start: number; end: number } | null {
	if (!currentTime) {
		return null;
	}

	let sentenceLow = 0;
	let sentenceHigh = sentences.length - 1;
	while (sentenceLow <= sentenceHigh) {
		const sentenceMid = Math.floor((sentenceLow + sentenceHigh) / 2);
		const sentence = sentences[sentenceMid];
		if (currentTime >= sentence.start && currentTime < sentence.end) {
			return { index: sentenceMid, start: sentence.start, end: sentence.end };
		}
		if (currentTime < sentence.start) {
			sentenceHigh = sentenceMid - 1;
		} else {
			sentenceLow = sentenceMid + 1;
		}
	}
	return null;
}

export default function ProjectTranscriptClient({ sentences, diarize }: { sentences: SentenceData[]; diarize: boolean }) {
	const dispatch = useMediaDispatch();
	const mediaCurrentTime = useMediaSelector((state) => state.mediaCurrentTime);
	const [activeItem, setActiveItem] = useState<{ index: number; start: number; end: number } | null>(null);
	const sentenceRefs = useRef<(HTMLDivElement | null)[]>([]);
	const containerRef = useRef<HTMLDivElement>(null);

	// Initialize refs array
	useEffect(() => {
		sentenceRefs.current = sentences.map(() => null);
	}, []);

	// Calculate highlighted subtitle index
	useEffect(() => {
		if (mediaCurrentTime !== undefined && activeItem) {
			if (mediaCurrentTime >= activeItem.start && mediaCurrentTime < activeItem.end) {
				return;
			}
		}
		const currentActiveItem = binarySearchSentence(sentences, mediaCurrentTime);
		if (currentActiveItem !== null) {
			setActiveItem(currentActiveItem);

			// Scroll to the active sentence only if it's not visible
			const activeElement = sentenceRefs.current[currentActiveItem.index];
			if (activeElement && containerRef.current) {
				// Check if the element is already visible in the viewport
				const containerRect = containerRef.current.getBoundingClientRect();
				const elementRect = activeElement.getBoundingClientRect();

				const isVisible = elementRect.top >= containerRect.top && elementRect.bottom <= containerRect.bottom;

				// Only scroll if the element is not visible
				if (!isVisible) {
					// Use container's scrollTop property instead of scrollIntoView
					// to keep scrolling within the container
					const scrollTop = activeElement.offsetTop - containerRef.current.offsetHeight + activeElement.offsetHeight;
					containerRef.current.scrollTo({
						top: scrollTop,
						behavior: "smooth",
					});
				}
			}
		}
	}, [mediaCurrentTime]);

	return (
		<div className="flex w-full flex-col">
			<div
				ref={containerRef}
				className={cn(
					"mx-auto flex max-h-[calc(100vh-260px)] w-full flex-col overflow-y-auto pb-4 pl-4",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-400/60",
				)}
			>
				<div>
					{sentences.map((sentence, sentenceIndex) => {
						return (
							<div
								key={sentenceIndex}
								className="w-full pr-4 pb-2.5"
								ref={(el) => {
									sentenceRefs.current[sentenceIndex] = el;
								}}
							>
								<div className="flex flex-col gap-1.5">
									<div className="flex flex-row items-center gap-1 pt-2.5">
										<div
											className="group flex h-[30px] cursor-pointer flex-row items-center gap-1.5 text-sm text-zinc-500 hover:text-blue-500 hover:underline"
											onClick={() => {
												dispatch({ type: MediaActionTypes.MEDIA_SEEK_REQUEST, detail: sentence.start });
												dispatch({ type: MediaActionTypes.MEDIA_PLAY_REQUEST });
											}}
										>
											{diarize ? (
												<p className="flex flex-row items-center gap-1 rounded-full border px-1.5 py-1">
													<User className="size-4 fill-current" />
													{sentence.speaker_id || "Speaker"}
												</p>
											) : (
												<p className="rounded-full border p-1.5">
													<Play className="h-3.5 w-3.5 fill-current text-zinc-400 group-hover:text-blue-500" />
												</p>
											)}
											{formatTime(sentence.start)}
										</div>
										{/* {!isNil(sentence.speaker) && (
											<div className="mb-1 flex flex-row items-center gap-1">
												<IconSpeaker className="h-5 w-5" fillColor={getSpeakerIconColor(sentence.speaker)} />
												<p className="text-sm text-black/90">{getSpeakerName(sentence.speaker) || "Speaker"}</p>
											</div>
										)} */}
									</div>

									<div className="text-black/85">
										<p className={cn("rounded-sm", activeItem?.index === sentenceIndex && "text-blue-500")}>{sentence.text}</p>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</div>
		</div>
	);
}
