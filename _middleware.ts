import { NextResponse, type NextRequest } from "next/server";
import { checkCfIpCountry } from "./server/auth/auth-ipcountry";

export async function middleware(request: NextRequest) {
	const isBanIpCountry = await checkCfIpCountry();
	if (isBanIpCountry) {
		return NextResponse.redirect(new URL("/forbidden", request.url));
	}

	return NextResponse.next();
}

export const config = {
	matcher: [
		// Skip all internal paths (_next)
		// '/((?!_next).*)',
		// '/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt).*)',
		// Matcher ignoring `/_next/` and `/api/`
		"/((?!api|static|_next|js|static|locales|favicon.ico|robots.txt|sitemap.xml|ads.txt|privacy-policy|terms-of-use|forbidden|admin|llms-full.txt|llms.txt).*)",
		// Optional: only run on root (/) URL
		// '/'
	],
};
