import { useState, useEffect } from "react";

export function useSubmitTimer1(isActive: boolean) {
	const [seconds, setSeconds] = useState<string>("00");
	const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

	useEffect(() => {
		if (isActive) {
			const id = setInterval(() => {
				setSeconds((prevSeconds) => (Number(prevSeconds) + 1).toFixed(0).padStart(2, "0"));
			}, 1000);
			setIntervalId(id);
		} else if (intervalId) {
			setSeconds("00");
			clearInterval(intervalId);
			setIntervalId(null);
		}

		return () => {
			if (intervalId) clearInterval(intervalId);
		};
	}, [isActive]);

	return { seconds };
}
