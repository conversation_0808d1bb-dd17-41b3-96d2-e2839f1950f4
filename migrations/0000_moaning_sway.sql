CREATE TABLE `better_auth_account` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`provider_id` text NOT NULL,
	`account_id` text NOT NULL,
	`access_token` text,
	`refresh_token` text,
	`access_token_expires_at` integer,
	`refresh_token_expires_at` integer,
	`scope` text,
	`id_token` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_account_uid_unique` ON `better_auth_account` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_account_user_uid_index` ON `better_auth_account` (`user_uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_account_provider_unique` ON `better_auth_account` (`account_id`,`provider_id`);--> statement-breakpoint
CREATE TABLE `blog_category` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`slug` text NOT NULL,
	`name` text,
	`description` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `blog_category_slug_unique` ON `blog_category` (`slug`);--> statement-breakpoint
CREATE TABLE `blog_item` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`slug` text NOT NULL,
	`category_id` integer,
	`lang` text DEFAULT 'en' NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`title` text NOT NULL,
	`meta_title` text,
	`meta_description` text,
	`image` text,
	`intro` text NOT NULL,
	`html` text,
	`publish_date` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `blog_item_slug_unique` ON `blog_item` (`slug`);--> statement-breakpoint
CREATE INDEX `blog_item_lang_index` ON `blog_item` (`lang`);--> statement-breakpoint
CREATE INDEX `blog_item_category_index` ON `blog_item` (`category_id`);--> statement-breakpoint
CREATE INDEX `blog_item_status_index` ON `blog_item` (`status`);--> statement-breakpoint
CREATE TABLE `changelog_item` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`lang` text DEFAULT 'en' NOT NULL,
	`major_version` integer,
	`minor_version` integer,
	`patch_version` integer,
	`status` integer DEFAULT 0 NOT NULL,
	`title` text NOT NULL,
	`image` text,
	`html` text,
	`publish_date` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `changelog_item_lang_index` ON `changelog_item` (`lang`);--> statement-breakpoint
CREATE INDEX `changelog_item_status_index` ON `changelog_item` (`status`);--> statement-breakpoint
CREATE TABLE `order` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`order_id` text NOT NULL,
	`source` text NOT NULL,
	`status` text NOT NULL,
	`billing_reason` text,
	`subscription_id` text,
	`checkout_id` text,
	`product_id` text,
	`currency` text,
	`subtotal_amount` integer,
	`discount_amount` integer,
	`tax_amount` integer,
	`net_amount` integer,
	`total_amount` integer,
	`refunded_amount` integer,
	`refunded_tax_amount` integer,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `order_order_id_unique` ON `order` (`order_id`);--> statement-breakpoint
CREATE INDEX `order_user_uid_index` ON `order` (`user_uid`);--> statement-breakpoint
CREATE TABLE `project` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`share` integer DEFAULT 0 NOT NULL,
	`file_name` text NOT NULL,
	`file_url_temp` text,
	`file_url_local` text,
	`duration` real DEFAULT 0 NOT NULL,
	`language` text NOT NULL,
	`speaker` integer,
	`speaker_info` text,
	`paragraphs_url_path` text,
	`is_transcript_empty` integer DEFAULT false NOT NULL,
	`credits_source` text,
	`error_info` text,
	`request_id` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `project_uid_unique` ON `project` (`uid`);--> statement-breakpoint
CREATE INDEX `project_user_id_index` ON `project` (`user_uid`);--> statement-breakpoint
CREATE INDEX `project_request_id_index` ON `project` (`request_id`);--> statement-breakpoint
CREATE TABLE `better_auth_session` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`token` text NOT NULL,
	`expires_at` integer NOT NULL,
	`ip_address` text,
	`user_agent` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_session_uid_unique` ON `better_auth_session` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_session_token_index` ON `better_auth_session` (`token`);--> statement-breakpoint
CREATE TABLE `subscription` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`subscription_id` text NOT NULL,
	`source` text NOT NULL,
	`status` text NOT NULL,
	`recurring_interval` text NOT NULL,
	`product_id` text NOT NULL,
	`checkout_id` text,
	`current_period_start_at` integer NOT NULL,
	`current_period_end_at` integer,
	`cancel_at_period_end` integer DEFAULT false NOT NULL,
	`canceled_at` integer,
	`start_at` integer,
	`end_at` integer,
	`ended_at` integer,
	`customer_cancellation_reason` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `subscription_subscription_id_unique` ON `subscription` (`subscription_id`);--> statement-breakpoint
CREATE INDEX `subscription_user_uid_index` ON `subscription` (`user_uid`);--> statement-breakpoint
CREATE TABLE `user_credits_history` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`credits_free` integer,
	`credits_one_time` integer,
	`credits_subscription` integer,
	`remark` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `user_credits_history_user_uid_index` ON `user_credits_history` (`user_uid`);--> statement-breakpoint
CREATE TABLE `user` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`name` text NOT NULL,
	`email` text NOT NULL,
	`email_verified` integer DEFAULT false,
	`image` text,
	`membership_id` integer DEFAULT 0 NOT NULL,
	`membership_formatted` text DEFAULT 'Free' NOT NULL,
	`credit_free` integer DEFAULT 0 NOT NULL,
	`credit_free_ends_at` integer,
	`credit_onetime` integer DEFAULT 0 NOT NULL,
	`credit_onetime_ends_at` integer,
	`credit_sub` integer DEFAULT 0 NOT NULL,
	`credit_sub_ends_at` integer,
	`sub_id` text,
	`sub_period` text DEFAULT 'none',
	`sub_invoice_ends_at` integer,
	`sub_expire_at` integer,
	`is_deleted` integer DEFAULT false NOT NULL,
	`ban` integer DEFAULT false NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_uid_unique` ON `user` (`uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `user_email_unique` ON `user` (`email`);--> statement-breakpoint
CREATE TABLE `better_auth_verification` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`identifier` text NOT NULL,
	`value` text NOT NULL,
	`expires_at` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_verification_uid_unique` ON `better_auth_verification` (`uid`);--> statement-breakpoint
CREATE INDEX `better_auth_verification_identifier_index` ON `better_auth_verification` (`identifier`);--> statement-breakpoint
CREATE INDEX `better_auth_verification_expires_at_index` ON `better_auth_verification` (`expires_at`);