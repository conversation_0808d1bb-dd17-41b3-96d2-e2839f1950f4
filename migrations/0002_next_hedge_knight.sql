CREATE TABLE `transcribe_task` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`platform` text NOT NULL,
	`request_id` text NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`file_name` text NOT NULL,
	`file_url_temp` text,
	`file_url_local` text,
	`duration` real DEFAULT 0 NOT NULL,
	`language` text NOT NULL,
	`speaker` integer,
	`credits_source` text,
	`ip` text,
	`project_uid` text,
	`remark` text,
	`error` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `transcribe_task_user_uid_index` ON `transcribe_task` (`user_uid`);--> statement-breakpoint
CREATE INDEX `transcribe_task_request_id_index` ON `transcribe_task` (`request_id`);