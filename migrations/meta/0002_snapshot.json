{"version": "6", "dialect": "sqlite", "id": "632f180d-57d1-42c3-a894-621c7f7dad73", "prevId": "9167d31c-b9e6-4569-a8a8-c0eade15e6e3", "tables": {"better_auth_account": {"name": "better_auth_account", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_account_uid_unique": {"name": "better_auth_account_uid_unique", "columns": ["uid"], "isUnique": true}, "better_auth_account_user_uid_index": {"name": "better_auth_account_user_uid_index", "columns": ["user_uid"], "isUnique": false}, "better_auth_account_provider_unique": {"name": "better_auth_account_provider_unique", "columns": ["account_id", "provider_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "blog_category": {"name": "blog_category", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"blog_category_slug_unique": {"name": "blog_category_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "blog_item": {"name": "blog_item", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "lang": {"name": "lang", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'en'"}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "meta_title": {"name": "meta_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "meta_description": {"name": "meta_description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "intro": {"name": "intro", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "publish_date": {"name": "publish_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"blog_item_slug_unique": {"name": "blog_item_slug_unique", "columns": ["slug"], "isUnique": true}, "blog_item_lang_index": {"name": "blog_item_lang_index", "columns": ["lang"], "isUnique": false}, "blog_item_category_index": {"name": "blog_item_category_index", "columns": ["category_id"], "isUnique": false}, "blog_item_status_index": {"name": "blog_item_status_index", "columns": ["status"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "changelog_item": {"name": "changelog_item", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "lang": {"name": "lang", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'en'"}, "major_version": {"name": "major_version", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "minor_version": {"name": "minor_version", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "patch_version": {"name": "patch_version", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "publish_date": {"name": "publish_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"changelog_item_lang_index": {"name": "changelog_item_lang_index", "columns": ["lang"], "isUnique": false}, "changelog_item_status_index": {"name": "changelog_item_status_index", "columns": ["status"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "order": {"name": "order", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "billing_reason": {"name": "billing_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "checkout_id": {"name": "checkout_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subtotal_amount": {"name": "subtotal_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "discount_amount": {"name": "discount_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "tax_amount": {"name": "tax_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "net_amount": {"name": "net_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_amount": {"name": "total_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refunded_amount": {"name": "refunded_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refunded_tax_amount": {"name": "refunded_tax_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"order_order_id_unique": {"name": "order_order_id_unique", "columns": ["order_id"], "isUnique": true}, "order_user_uid_index": {"name": "order_user_uid_index", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "project": {"name": "project", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "share": {"name": "share", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_url_temp": {"name": "file_url_temp", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_url_local": {"name": "file_url_local", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "speaker": {"name": "speaker", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "speaker_info": {"name": "speaker_info", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paragraphs_url_path": {"name": "paragraphs_url_path", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_transcript_empty": {"name": "is_transcript_empty", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "credits_source": {"name": "credits_source", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_info": {"name": "error_info", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"project_uid_unique": {"name": "project_uid_unique", "columns": ["uid"], "isUnique": true}, "project_user_id_index": {"name": "project_user_id_index", "columns": ["user_uid"], "isUnique": false}, "project_request_id_index": {"name": "project_request_id_index", "columns": ["request_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "better_auth_session": {"name": "better_auth_session", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_session_uid_unique": {"name": "better_auth_session_uid_unique", "columns": ["uid"], "isUnique": true}, "better_auth_session_token_index": {"name": "better_auth_session_token_index", "columns": ["token"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription": {"name": "subscription", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "recurring_interval": {"name": "recurring_interval", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "checkout_id": {"name": "checkout_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_period_start_at": {"name": "current_period_start_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_end_at": {"name": "current_period_end_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "canceled_at": {"name": "canceled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_at": {"name": "start_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_at": {"name": "end_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "ended_at": {"name": "ended_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_cancellation_reason": {"name": "customer_cancellation_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"subscription_subscription_id_unique": {"name": "subscription_subscription_id_unique", "columns": ["subscription_id"], "isUnique": true}, "subscription_user_uid_index": {"name": "subscription_user_uid_index", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "transcribe_task": {"name": "transcribe_task", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_url_temp": {"name": "file_url_temp", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_url_local": {"name": "file_url_local", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "speaker": {"name": "speaker", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_source": {"name": "credits_source", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "project_uid": {"name": "project_uid", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"transcribe_task_user_uid_index": {"name": "transcribe_task_user_uid_index", "columns": ["user_uid"], "isUnique": false}, "transcribe_task_request_id_index": {"name": "transcribe_task_request_id_index", "columns": ["request_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_credits_history": {"name": "user_credits_history", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "credits_free": {"name": "credits_free", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_one_time": {"name": "credits_one_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_subscription": {"name": "credits_subscription", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_credits_history_user_uid_index": {"name": "user_credits_history_user_uid_index", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "membership_id": {"name": "membership_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "membership_formatted": {"name": "membership_formatted", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Free'"}, "credit_free": {"name": "credit_free", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_free_ends_at": {"name": "credit_free_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit_onetime": {"name": "credit_onetime", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_onetime_ends_at": {"name": "credit_onetime_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit_sub": {"name": "credit_sub", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_sub_ends_at": {"name": "credit_sub_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_id": {"name": "sub_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_period": {"name": "sub_period", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'none'"}, "sub_invoice_ends_at": {"name": "sub_invoice_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_expire_at": {"name": "sub_expire_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "ban": {"name": "ban", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_uid_unique": {"name": "user_uid_unique", "columns": ["uid"], "isUnique": true}, "user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "better_auth_verification": {"name": "better_auth_verification", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_verification_uid_unique": {"name": "better_auth_verification_uid_unique", "columns": ["uid"], "isUnique": true}, "better_auth_verification_identifier_index": {"name": "better_auth_verification_identifier_index", "columns": ["identifier"], "isUnique": false}, "better_auth_verification_expires_at_index": {"name": "better_auth_verification_expires_at_index", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}